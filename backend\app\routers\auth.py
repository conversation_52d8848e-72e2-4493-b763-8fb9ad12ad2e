from datetime import timedelta, datetime
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials

from ..models.auth import LoginCredentials, LoginResponse, Token, PasswordChange
from ..models.user import UserInDB, UserProfile
from ..auth import (
    authenticate_user,
    create_access_token,
    get_current_active_user,
    get_password_hash,
    verify_password,
    security
)
from ..database import get_users_collection
from ..config import settings

router = APIRouter()


@router.post("/login", response_model=LoginResponse)
async def login(credentials: LoginCredentials):
    """Authenticate user and return access token"""
    print(f"[LOGIN] Login attempt for email: {credentials.email}")

    try:
        user = await authenticate_user(credentials.email, credentials.password)
        print(f"[LOGIN] Authentication result: {'Success' if user else 'Failed'}")

        if not user:
            print(f"[LOGIN] Authentication failed for {credentials.email}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Email ou mot de passe incorrect",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except Exception as e:
        print(f"[LOGIN] Error during authentication: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during authentication"
        )
    
    # Create access token
    print(f"[LOGIN] Creating access token for user: {user.email}")
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.email, "user_id": str(user.id)},
        expires_delta=access_token_expires
    )
    print(f"[LOGIN] Access token created successfully")

    # Return user profile without password
    user_profile = UserProfile(
        id=str(user.id),
        email=user.email,
        firstName=user.firstName,
        lastName=user.lastName,
        role=user.role,
        department=user.department,
        position=user.position,
        phone=user.phone,
        profilePicture=user.profilePicture,
        hireDate=user.hireDate,
        workSchedule=user.workSchedule
    )
    
    login_response = LoginResponse(
        access_token=access_token,
        token_type="bearer",
        user=user_profile.dict()
    )

    print(f"[LOGIN] Login successful for {user.email}, returning response")
    return login_response


@router.post("/logout")
async def logout(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Logout user (client should discard token)"""
    # In a real implementation, you might want to blacklist the token
    # For now, we just return success and let the client handle token removal
    return {"message": "Successfully logged out"}


@router.get("/verify")
async def verify_token(current_user: UserInDB = Depends(get_current_active_user)):
    """Verify if the current token is valid"""
    return {
        "valid": True,
        "user": {
            "id": str(current_user.id),
            "email": current_user.email,
            "firstName": current_user.firstName,
            "lastName": current_user.lastName,
            "role": current_user.role
        }
    }


@router.get("/me", response_model=UserProfile)
async def get_current_user_profile(current_user: UserInDB = Depends(get_current_active_user)):
    """Get current user profile"""
    return UserProfile(
        id=str(current_user.id),
        email=current_user.email,
        firstName=current_user.firstName,
        lastName=current_user.lastName,
        role=current_user.role,
        department=current_user.department,
        position=current_user.position,
        phone=current_user.phone,
        profilePicture=current_user.profilePicture,
        hireDate=current_user.hireDate,
        workSchedule=current_user.workSchedule
    )


@router.post("/change-password")
async def change_password(
    password_data: PasswordChange,
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Change user password"""
    # Verify current password
    if not verify_password(password_data.current_password, current_user.password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )
    
    # Update password in database
    users_collection = get_users_collection()
    hashed_password = get_password_hash(password_data.new_password)
    
    await users_collection.update_one(
        {"_id": current_user.id},
        {"$set": {"password": hashed_password, "updatedAt": datetime.utcnow()}}
    )
    
    return {"message": "Password changed successfully"}
