import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useState, useEffect } from 'react';
import { ScreenWrapper } from '@/components/ui/ScreenWrapper';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { InputField } from '@/components/ui/InputField';
import { Modal } from '@/components/ui/Modal';
import { useAppSelector } from '@/store/store';
import { selectCurrentUser } from '@/store/slices/authSlice';
import {
  useGetAllUsersQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
} from '@/store/api/apiSlice';
import { User as UserType } from '@/types/auth';
import {
  Plus,
  Search,
  User,
  Mail,
  Briefcase,
  MoveVertical as MoreVertical,
} from 'lucide-react-native';

export default function UserManagementScreen() {
  const user = useAppSelector(selectCurrentUser);

  // Route guard: Only admins can access this screen
  if (!user || user.role !== 'admin') {
    return (
      <ScreenWrapper>
        <View style={styles.unauthorizedContainer}>
          <Text style={styles.unauthorizedText}>Accès non autorisé</Text>
          <Text style={styles.unauthorizedSubtext}>
            Cette page est réservée aux administrateurs.
          </Text>
        </View>
      </ScreenWrapper>
    );
  }

  const [searchQuery, setSearchQuery] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    department: '',
    role: 'employee' as 'employee' | 'manager' | 'admin',
    position: '',
    password: '',
  });

  // RTK Query hooks
  const {
    data: usersData,
    isLoading: isLoadingUsers,
    error: usersError,
    refetch,
  } = useGetAllUsersQuery({ search: searchQuery });

  const [createUser, { isLoading: isCreating }] = useCreateUserMutation();
  const [updateUser, { isLoading: isUpdating }] = useUpdateUserMutation();
  const [deleteUser, { isLoading: isDeleting }] = useDeleteUserMutation();

  const users = usersData?.data || [];

  const filteredUsers = users.filter(
    (user) =>
      user.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSubmit = async () => {
    if (
      !formData.firstName ||
      !formData.lastName ||
      !formData.email ||
      !formData.department ||
      !formData.position ||
      (!selectedUser && !formData.password)
    ) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs obligatoires');
      return;
    }

    try {
      if (selectedUser) {
        // For updates, exclude password if not provided
        const { password, ...updateData } = formData;
        const finalUpdateData = password
          ? { ...updateData, password }
          : updateData;
        await updateUser({
          id: selectedUser.id,
          updates: finalUpdateData,
        }).unwrap();
        Alert.alert('Succès', 'Utilisateur mis à jour avec succès');
      } else {
        await createUser(formData).unwrap();
        Alert.alert('Succès', 'Utilisateur créé avec succès');
      }
      setShowModal(false);
      resetForm();
    } catch (error: any) {
      console.error('Erreur:', error);
      Alert.alert(
        'Erreur',
        error?.data?.message || "Impossible de sauvegarder l'utilisateur"
      );
    }
  };

  const resetForm = () => {
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      department: '',
      role: 'employee',
      position: '',
      password: '',
    });
    setSelectedUser(null);
  };

  const handleEdit = (user: UserType) => {
    setSelectedUser(user);
    setFormData({
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      department: user?.department || '',
      role: user?.role || 'employee',
      position: user?.position || '',
      password: '', // Password field is not populated for security
    });
    setShowModal(true);
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'danger';
      case 'manager':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Admin';
      case 'manager':
        return 'Manager';
      default:
        return 'Employé';
    }
  };

  const renderUser = ({ item }: { item: any }) => {
    // Safely handle potentially undefined user data
    const firstName = item?.firstName || '';
    const lastName = item?.lastName || '';
    const email = item?.email || '';
    const department = item?.department || '';
    const fullName = `${firstName} ${lastName}`.trim() || 'Nom inconnu';

    return (
      <Card style={styles.userCard}>
        <View style={styles.userHeader}>
          <View style={styles.userInfo}>
            <View style={styles.avatar}>
              <User size={20} color="#ffffff" />
            </View>
            <View style={styles.userDetails}>
              <Text style={styles.userName}>{fullName}</Text>
              <View style={styles.userMeta}>
                <Mail size={14} color="#6b7280" />
                <Text style={styles.userEmail}>{email}</Text>
              </View>
              <View style={styles.userMeta}>
                <Briefcase size={14} color="#6b7280" />
                <Text style={styles.userDepartment}>{department}</Text>
              </View>
            </View>
          </View>
          <View style={styles.userActions}>
            <Badge
              variant={getRoleBadgeVariant(item?.role || 'employee')}
              text={getRoleLabel(item?.role || 'employee')}
            />
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleEdit(item)}
            >
              <MoreVertical size={20} color="#6b7280" />
            </TouchableOpacity>
          </View>
        </View>
      </Card>
    );
  };

  // Show loading state while fetching users
  if (isLoadingUsers) {
    return (
      <ScreenWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3b82f6" />
          <Text style={styles.loadingText}>Chargement des utilisateurs...</Text>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Gestion des Utilisateurs</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowModal(true)}
          >
            <Plus size={24} color="#ffffff" />
          </TouchableOpacity>
        </View>

        <View style={styles.searchContainer}>
          <View style={styles.searchWrapper}>
            <Search size={20} color="#6b7280" />
            <InputField
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Rechercher un utilisateur..."
              style={styles.searchInput}
            />
          </View>
        </View>

        {users.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Aucun utilisateur trouvé</Text>
          </View>
        ) : (
          <FlatList
            data={users}
            renderItem={renderUser}
            keyExtractor={(item, index) => item?.id || `user-${index}`}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}

        <Modal
          visible={showModal}
          onClose={() => {
            setShowModal(false);
            resetForm();
          }}
          title={selectedUser ? "Modifier l'utilisateur" : 'Nouvel utilisateur'}
        >
          <View style={styles.modalContent}>
            <InputField
              label="Prénom"
              value={formData.firstName}
              onChangeText={(text) =>
                setFormData({ ...formData, firstName: text })
              }
              placeholder="Prénom"
            />
            <InputField
              label="Nom"
              value={formData.lastName}
              onChangeText={(text) =>
                setFormData({ ...formData, lastName: text })
              }
              placeholder="Nom"
            />
            <InputField
              label="Email"
              value={formData.email}
              onChangeText={(text) => setFormData({ ...formData, email: text })}
              placeholder="<EMAIL>"
              keyboardType="email-address"
            />
            <InputField
              label="Département"
              value={formData.department}
              onChangeText={(text) =>
                setFormData({ ...formData, department: text })
              }
              placeholder="Département"
            />
            <InputField
              label="Poste"
              value={formData.position}
              onChangeText={(text) =>
                setFormData({ ...formData, position: text })
              }
              placeholder="Poste occupé"
            />
            <InputField
              label="Rôle"
              value={formData.role}
              onChangeText={(text) =>
                setFormData({
                  ...formData,
                  role: text as 'employee' | 'manager' | 'admin',
                })
              }
              placeholder="employee | manager | admin"
            />
            {!selectedUser && (
              <InputField
                label="Mot de passe *"
                value={formData.password}
                onChangeText={(text) =>
                  setFormData({ ...formData, password: text })
                }
                placeholder="Mot de passe (min. 6 caractères)"
                secureTextEntry
              />
            )}
            <Button
              title={selectedUser ? 'Modifier' : 'Créer'}
              onPress={handleSubmit}
              loading={isCreating || isUpdating}
              style={styles.submitButton}
            />
          </View>
        </Modal>
      </View>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#111827',
  },
  addButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#3B82F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  searchWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: '#111827',
  },
  listContainer: {
    padding: 16,
  },
  userCard: {
    marginBottom: 12,
  },
  userHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  userInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#3B82F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  userMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  userEmail: {
    marginLeft: 6,
    fontSize: 14,
    color: '#6b7280',
  },
  userDepartment: {
    marginLeft: 6,
    fontSize: 14,
    color: '#6b7280',
  },
  userActions: {
    alignItems: 'flex-end',
  },
  actionButton: {
    marginTop: 8,
    padding: 4,
  },
  modalContent: {
    gap: 16,
  },
  submitButton: {
    marginTop: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  unauthorizedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  unauthorizedText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ef4444',
    textAlign: 'center',
  },
  unauthorizedSubtext: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 8,
  },
});
