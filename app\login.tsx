import { View, Text, StyleSheet, Alert } from 'react-native';
import { useState } from 'react';
import { router } from 'expo-router';
import { ScreenWrapper } from '@/components/ui/ScreenWrapper';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { InputField } from '@/components/ui/InputField';
import { useLoginMutation } from '@/store/api/apiSlice';
import { AuthService } from '@/services/authService';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [login, { isLoading }] = useLoginMutation();

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs');
      return;
    }

    console.log('[Login] Attempting login with:', { email, password: '***' });

    try {
      // First, test backend connectivity
      console.log('[Login] Testing backend connectivity...');
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        const healthResponse = await fetch('http://10.1.0.168:8000/health', {
          method: 'GET',
          signal: controller.signal,
        });
        clearTimeout(timeoutId);
        console.log('[Login] Backend health check:', healthResponse.status);
      } catch (healthError) {
        console.error('[Login] Backend connectivity test failed:', healthError);
        Alert.alert(
          'Erreur de connexion',
          'Impossible de se connecter au serveur. Vérifiez votre connexion réseau.'
        );
        return;
      }

      // Try direct fetch first to test the endpoint
      console.log('[Login] 🧪 Testing direct fetch to login endpoint...');
      try {
        const directController = new AbortController();
        const directTimeoutId = setTimeout(
          () => directController.abort(),
          10000
        );

        const directResponse = await fetch(
          'http://10.1.0.168:8000/api/auth/login',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, password }),
            signal: directController.signal,
          }
        );

        clearTimeout(directTimeoutId);
        console.log(
          '[Login] Direct fetch response status:',
          directResponse.status
        );

        if (directResponse.ok) {
          const directResult = await directResponse.json();
          console.log('[Login] Direct fetch successful:', directResult);

          // Save auth data using AuthService
          console.log('[Login] Saving auth data from direct fetch...');
          await AuthService.saveAuthData(
            directResult.user,
            directResult.access_token,
            undefined
          );
          console.log(
            '[Login] Auth data saved, navigation should happen automatically'
          );
          return; // Exit early since direct fetch worked
        } else {
          const errorText = await directResponse.text();
          console.error('[Login] Direct fetch failed:', errorText);
        }
      } catch (directError) {
        console.error('[Login] Direct fetch error:', directError);
      }

      // Use RTK Query mutation with timeout handling
      console.log('[Login] 🚀 Calling RTK Query login mutation...');

      // Add a timeout promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(
          () => reject(new Error('Login timeout - request took too long')),
          15000
        ); // 15 second timeout
      });

      const loginPromise = login({ email, password }).unwrap();

      const result = (await Promise.race([
        loginPromise,
        timeoutPromise,
      ])) as any;
      console.log('[Login] RTK Query login mutation successful:', result);

      // Save auth data using AuthService
      console.log('[Login] Saving auth data...');
      await AuthService.saveAuthData(
        result.user,
        result.access_token,
        undefined
      );
      console.log(
        '[Login] Auth data saved, navigation should happen automatically'
      );

      // Add a small delay to ensure state updates are processed
      setTimeout(() => {
        console.log('[Login] Checking auth state after save...');
        const isAuth = AuthService.isAuthenticated();
        console.log('[Login] Current auth state:', isAuth);

        // Force navigation if automatic navigation doesn't work
        if (isAuth) {
          console.log('[Login] Forcing navigation to tabs...');
          router.replace('/(tabs)');
        }
      }, 200);

      // Navigation will be handled by AuthNavigator
    } catch (error: any) {
      console.error('[Login] ❌ Login error details:');
      console.error('[Login] 🔍 Error object:', error);
      console.error('[Login] 📊 Error status:', error?.status);
      console.error('[Login] 📝 Error data:', error?.data);
      console.error('[Login] 🌐 Error message:', error?.message);
      console.error('[Login] 🔧 Full error:', JSON.stringify(error, null, 2));

      let errorMessage = 'Email ou mot de passe incorrect';

      if (error?.message === 'Login timeout - request took too long') {
        errorMessage = 'La connexion a pris trop de temps. Veuillez réessayer.';
        console.error('[Login] ⏰ Login timeout detected');
      } else if (error?.status === 'FETCH_ERROR') {
        errorMessage =
          'Impossible de se connecter au serveur. Vérifiez votre connexion réseau.';
        console.error(
          '[Login] 🌐 Network error detected - cannot reach server'
        );
      } else if (error?.data?.message) {
        errorMessage = error.data.message;
      }

      Alert.alert('Erreur', errorMessage);
    }
  };

  return (
    <ScreenWrapper>
      {/* <AuthDebug /> */}
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Connexion</Text>
          <Text style={styles.subtitle}>Accédez à votre espace de travail</Text>
        </View>

        <Card style={styles.card}>
          <InputField
            label="Email"
            value={email}
            onChangeText={setEmail}
            placeholder="<EMAIL>"
            keyboardType="email-address"
          />

          <InputField
            label="Mot de passe"
            value={password}
            onChangeText={setPassword}
            placeholder="Votre mot de passe"
            secureTextEntry={!showPassword}
          />

          <Button
            title="Se connecter"
            onPress={handleLogin}
            loading={isLoading}
            style={styles.loginButton}
          />

          <Button
            title="Mot de passe oublié ?"
            variant="secondary"
            onPress={() => {}}
            style={styles.forgotButton}
          />
        </Card>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Première connexion ? Contactez votre administrateur
          </Text>
        </View>
      </View>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
    backgroundColor: '#f9fafb',
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  card: {
    marginBottom: 24,
  },
  loginButton: {
    marginTop: 8,
  },
  forgotButton: {
    marginTop: 12,
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#9ca3af',
    textAlign: 'center',
  },
});
