export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: 'employee' | 'manager' | 'admin';
  department: string;
  hireDate: string;
  avatar?: string;
  phone?: string;
  position?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  user: User;
}
