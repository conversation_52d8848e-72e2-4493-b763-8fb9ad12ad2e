from datetime import datetime, timedelta
from typing import Optional
from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from bson import ObjectId

from .config import settings
from .database import get_users_collection
from .models.auth import TokenData
from .models.user import UserInDB

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT token security
security = HTTPBearer()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Hash a password"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt


async def get_user_by_email(email: str) -> Optional[UserInDB]:
    """Get user by email from database"""
    users_collection = get_users_collection()
    user_data = await users_collection.find_one({"email": email, "isActive": True})
    
    if user_data:
        return UserInDB(**user_data)
    return None


async def get_user_by_id(user_id: str) -> Optional[UserInDB]:
    """Get user by ID from database"""
    users_collection = get_users_collection()
    
    try:
        object_id = ObjectId(user_id)
        user_data = await users_collection.find_one({"_id": object_id, "isActive": True})
        
        if user_data:
            return UserInDB(**user_data)
    except Exception:
        pass
    
    return None


async def authenticate_user(email: str, password: str) -> Optional[UserInDB]:
    """Authenticate user with email and password"""
    user = await get_user_by_email(email)
    if not user:
        return None
    if not verify_password(password, user.password):
        return None
    return user


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> UserInDB:
    """Get current authenticated user from JWT token"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        print(f"[AUTH] Attempting to decode token: {credentials.credentials[:20]}...")
        payload = jwt.decode(credentials.credentials, settings.secret_key, algorithms=[settings.algorithm])
        print(f"[AUTH] Token decoded successfully. Payload: {payload}")

        email: str = payload.get("sub")
        user_id: str = payload.get("user_id")

        if email is None or user_id is None:
            print(f"[AUTH] Missing email or user_id in token. Email: {email}, User ID: {user_id}")
            raise credentials_exception

        token_data = TokenData(email=email, user_id=user_id)
        print(f"[AUTH] Token data extracted: {token_data}")
    except JWTError as e:
        print(f"[AUTH] JWT Error: {e}")
        raise credentials_exception
    except Exception as e:
        print(f"[AUTH] Unexpected error during token validation: {e}")
        raise credentials_exception

    user = await get_user_by_id(token_data.user_id)
    if user is None:
        print(f"[AUTH] User not found for ID: {token_data.user_id}")
        raise credentials_exception

    print(f"[AUTH] User authenticated successfully: {user.email}")
    return user
    
    return user


async def get_current_active_user(current_user: UserInDB = Depends(get_current_user)) -> UserInDB:
    """Get current active user"""
    if not current_user.isActive:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


async def require_role(required_roles: list[str]):
    """Dependency to require specific roles"""
    def role_checker(current_user: UserInDB = Depends(get_current_active_user)) -> UserInDB:
        if current_user.role not in required_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions"
            )
        return current_user
    return role_checker


# Role-specific dependencies
async def require_admin(current_user: UserInDB = Depends(get_current_active_user)) -> UserInDB:
    """Require admin role"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user


async def require_manager_or_admin(current_user: UserInDB = Depends(get_current_active_user)) -> UserInDB:
    """Require manager or admin role"""
    if current_user.role not in ["manager", "admin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Manager or admin access required"
        )
    return current_user
