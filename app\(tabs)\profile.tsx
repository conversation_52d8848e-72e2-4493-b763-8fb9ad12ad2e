import {
  View,
  Text,
  StyleSheet,
  Al<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>View,
} from 'react-native';
import { ScreenWrapper } from '@/components/ui/ScreenWrapper';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useAppSelector } from '@/store/store';
import { selectCurrentUser } from '@/store/slices/authSlice';
import { AuthService } from '@/services/authService';
import {
  User,
  Mail,
  Briefcase,
  Calendar,
  LogOut,
  Settings,
} from 'lucide-react-native';

export default function ProfileScreen() {
  const user = useAppSelector(selectCurrentUser);

  // Guard against null user
  if (!user) {
    return (
      <ScreenWrapper>
        <View style={styles.container}>
          <Text>Chargement du profil...</Text>
        </View>
      </ScreenWrapper>
    );
  }

  const handleLogout = async () => {
    Alert.alert('Déconnexion', 'Êtes-vous sûr de vouloir vous déconnecter ?', [
      { text: 'Annuler', style: 'cancel' },
      {
        text: 'Déconnecter',
        onPress: async () => {
          try {
            await AuthService.logout();
          } catch (error) {
            console.error('Logout error:', error);
            // Even if logout fails, we should still proceed
          }
        },
        style: 'destructive',
      },
    ]);
  };

  return (
    <ScreenWrapper>
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.avatarContainer}>
            <View style={styles.avatar}>
              <User size={32} color="#ffffff" />
            </View>
            <Text style={styles.name}>
              {user?.firstName} {user?.lastName}
            </Text>
            <Text style={styles.role}>{user?.role}</Text>
          </View>
        </View>
        <ScrollView style={styles.scrollContent}>
          <Card style={styles.infoCard}>
            <Text style={styles.cardTitle}>Informations personnelles</Text>
            <View style={styles.infoRow}>
              <Mail size={20} color="#6b7280" />
              <View style={styles.infoText}>
                <Text style={styles.infoLabel}>Email</Text>
                <Text style={styles.infoValue}>{user?.email}</Text>
              </View>
            </View>

            <View style={styles.infoRow}>
              <Briefcase size={20} color="#6b7280" />
              <View style={styles.infoText}>
                <Text style={styles.infoLabel}>Département</Text>
                <Text style={styles.infoValue}>{user?.department}</Text>
              </View>
            </View>

            <View style={styles.infoRow}>
              <Calendar size={20} color="#6b7280" />
              <View style={styles.infoText}>
                <Text style={styles.infoLabel}>Date d'embauche</Text>
                <Text style={styles.infoValue}>{user?.hireDate}</Text>
              </View>
            </View>
          </Card>

          {user?.role === 'employee' && (
            <Card style={styles.balanceCard}>
              <Text style={styles.cardTitle}>Solde de congés</Text>
              <View style={styles.balanceRow}>
                <View style={styles.balanceItem}>
                  <Text style={styles.balanceValue}>25</Text>
                  <Text style={styles.balanceLabel}>Jours acquis</Text>
                </View>
                <View style={styles.balanceItem}>
                  <Text style={styles.balanceValue}>10</Text>
                  <Text style={styles.balanceLabel}>Jours pris</Text>
                </View>
                <View style={styles.balanceItem}>
                  <Text style={styles.balanceValueHighlight}>15</Text>
                  <Text style={styles.balanceLabel}>Jours restants</Text>
                </View>
              </View>
            </Card>
          )}

          <Card style={styles.actionsCard}>
            <Text style={styles.cardTitle}>Actions</Text>

            <Button
              title="Paramètres"
              variant="secondary"
              onPress={() => {}}
              style={styles.actionButton}
              leftIcon={<Settings size={20} color="#374151" />}
            />

            <Button
              title="Se déconnecter"
              variant="danger"
              onPress={handleLogout}
              style={styles.actionButton}
              leftIcon={<LogOut size={20} color="#ffffff" />}
            />
          </Card>
        </ScrollView>
      </View>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    paddingBottom: 24,
  },
  avatarContainer: {
    alignItems: 'center',
    paddingTop: 32,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#3B82F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  name: {
    fontSize: 24,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 4,
  },
  role: {
    fontSize: 16,
    color: '#6b7280',
    textTransform: 'capitalize',
  },
  infoCard: {
    margin: 16,
    marginBottom: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  infoText: {
    marginLeft: 12,
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  infoValue: {
    fontSize: 16,
    color: '#111827',
    fontWeight: '500',
    marginTop: 2,
  },
  balanceCard: {
    margin: 16,
    marginBottom: 12,
  },
  balanceRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  balanceItem: {
    alignItems: 'center',
  },
  balanceValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#374151',
    marginBottom: 4,
  },
  balanceValueHighlight: {
    fontSize: 24,
    fontWeight: '700',
    color: '#10B981',
    marginBottom: 4,
  },
  balanceLabel: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
  },
  actionsCard: {
    margin: 16,
  },
  actionButton: {
    marginBottom: 12,
  },
  scrollContent: {
    flex: 1,
  },
});
