from datetime import datetime, date, timedelta
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from bson import ObjectId

from ..models.attendance import (
    AttendanceRecord, AttendanceStatus, AttendanceHistory, 
    AttendanceStats, ClockActionPayload, AttendanceCreate
)
from ..models.user import UserInDB
from ..auth import get_current_active_user
from ..database import get_attendance_collection

router = APIRouter()


def calculate_work_time(clock_in: datetime, clock_out: datetime, break_time: int = 60) -> dict:
    """Calculate work time, overtime, and status"""
    total_minutes = int((clock_out - clock_in).total_seconds() / 60)
    work_minutes = total_minutes - break_time
    
    # Standard work day is 8 hours (480 minutes)
    standard_work_minutes = 480
    overtime_minutes = max(0, work_minutes - standard_work_minutes)
    
    # Determine status based on clock-in time
    # Assuming work starts at 9:00 AM
    work_start_time = clock_in.replace(hour=9, minute=0, second=0, microsecond=0)
    late_threshold = work_start_time + timedelta(minutes=15)  # 15 minutes grace period
    
    if clock_in <= late_threshold:
        status = "present"
    else:
        status = "late"
    
    return {
        "totalTime": work_minutes,
        "overtimeHours": overtime_minutes,
        "status": status
    }


@router.get("/status", response_model=AttendanceStatus)
async def get_attendance_status(current_user: UserInDB = Depends(get_current_active_user)):
    """Get current attendance status for today"""
    attendance_collection = get_attendance_collection()
    
    # Get today's date
    today = datetime.utcnow().date()
    start_of_day = datetime.combine(today, datetime.min.time())
    end_of_day = datetime.combine(today, datetime.max.time())
    
    # Find today's attendance record
    attendance_record = await attendance_collection.find_one({
        "userId": current_user.id,
        "date": {"$gte": start_of_day, "$lte": end_of_day}
    })
    
    if not attendance_record:
        return AttendanceStatus(
            isClockedIn=False,
            clockInTime=None,
            clockOutTime=None,
            totalTime=0,
            status="absent"
        )
    
    is_clocked_in = attendance_record.get("clockIn") is not None and attendance_record.get("clockOut") is None
    
    return AttendanceStatus(
        isClockedIn=is_clocked_in,
        clockInTime=attendance_record.get("clockIn"),
        clockOutTime=attendance_record.get("clockOut"),
        totalTime=attendance_record.get("totalTime", 0),
        status=attendance_record.get("status", "absent")
    )


@router.post("/clock-in", response_model=AttendanceRecord)
async def clock_in(
    payload: ClockActionPayload,
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Clock in for the day"""
    attendance_collection = get_attendance_collection()
    
    # Get today's date
    today = datetime.utcnow().date()
    start_of_day = datetime.combine(today, datetime.min.time())
    end_of_day = datetime.combine(today, datetime.max.time())
    
    # Check if already clocked in today
    existing_record = await attendance_collection.find_one({
        "userId": current_user.id,
        "date": {"$gte": start_of_day, "$lte": end_of_day}
    })
    
    if existing_record and existing_record.get("clockIn"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Already clocked in today"
        )
    
    now = datetime.utcnow()
    
    # Determine status based on clock-in time
    work_start_time = now.replace(hour=9, minute=0, second=0, microsecond=0)
    late_threshold = work_start_time + timedelta(minutes=15)
    attendance_status = "late" if now > late_threshold else "present"
    
    # Create or update attendance record
    attendance_data = {
        "userId": current_user.id,
        "date": start_of_day,
        "clockIn": now,
        "clockOut": None,
        "totalTime": 0,
        "breakTime": 60,  # Default 1 hour break
        "overtimeHours": 0,
        "status": attendance_status,
        "location": payload.location.dict() if payload.location else None,
        "notes": payload.notes,
        "isManualEntry": False,
        "updatedAt": now
    }
    
    if existing_record:
        # Update existing record
        await attendance_collection.update_one(
            {"_id": existing_record["_id"]},
            {"$set": attendance_data}
        )
        record_id = existing_record["_id"]
    else:
        # Create new record
        attendance_data["createdAt"] = now
        result = await attendance_collection.insert_one(attendance_data)
        record_id = result.inserted_id
    
    # Get the updated/created record
    record = await attendance_collection.find_one({"_id": record_id})
    
    return AttendanceRecord(
        id=str(record["_id"]),
        userId=str(record["userId"]),
        date=record["date"],
        clockIn=record["clockIn"],
        clockOut=record["clockOut"],
        totalTime=record["totalTime"],
        breakTime=record["breakTime"],
        overtimeHours=record["overtimeHours"],
        status=record["status"],
        location=record.get("location"),
        notes=record.get("notes"),
        isManualEntry=record["isManualEntry"],
        createdAt=record["createdAt"],
        updatedAt=record["updatedAt"]
    )


@router.post("/clock-out", response_model=AttendanceRecord)
async def clock_out(
    payload: ClockActionPayload,
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Clock out for the day"""
    attendance_collection = get_attendance_collection()

    # Get today's date
    today = datetime.utcnow().date()
    start_of_day = datetime.combine(today, datetime.min.time())
    end_of_day = datetime.combine(today, datetime.max.time())

    # Find today's attendance record
    existing_record = await attendance_collection.find_one({
        "userId": current_user.id,
        "date": {"$gte": start_of_day, "$lte": end_of_day}
    })

    if not existing_record:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No clock-in record found for today"
        )

    if not existing_record.get("clockIn"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Must clock in before clocking out"
        )

    if existing_record.get("clockOut"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Already clocked out today"
        )

    now = datetime.utcnow()
    clock_in_time = existing_record["clockIn"]

    # Calculate work time and overtime
    work_stats = calculate_work_time(clock_in_time, now, existing_record.get("breakTime", 60))

    # Update attendance record
    update_data = {
        "clockOut": now,
        "totalTime": work_stats["totalTime"],
        "overtimeHours": work_stats["overtimeHours"],
        "status": work_stats["status"],
        "updatedAt": now
    }

    # Update location and notes if provided
    if payload.location:
        update_data["location"] = payload.location.dict()
    if payload.notes:
        update_data["notes"] = payload.notes

    await attendance_collection.update_one(
        {"_id": existing_record["_id"]},
        {"$set": update_data}
    )

    # Get updated record
    record = await attendance_collection.find_one({"_id": existing_record["_id"]})

    return AttendanceRecord(
        id=str(record["_id"]),
        userId=str(record["userId"]),
        date=record["date"],
        clockIn=record["clockIn"],
        clockOut=record["clockOut"],
        totalTime=record["totalTime"],
        breakTime=record["breakTime"],
        overtimeHours=record["overtimeHours"],
        status=record["status"],
        location=record.get("location"),
        notes=record.get("notes"),
        isManualEntry=record["isManualEntry"],
        createdAt=record["createdAt"],
        updatedAt=record["updatedAt"]
    )


@router.get("/history", response_model=AttendanceHistory)
async def get_attendance_history(
    month: Optional[int] = Query(None, ge=1, le=12),
    year: Optional[int] = Query(None, ge=2020),
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Get attendance history for a specific month/year"""
    attendance_collection = get_attendance_collection()

    # Default to current month/year if not provided
    now = datetime.utcnow()
    target_month = month or now.month
    target_year = year or now.year

    print(f"[API] Getting attendance history for user {current_user.id}, month: {target_month}, year: {target_year}")

    # Calculate date range for the month
    start_date = datetime(target_year, target_month, 1)
    if target_month == 12:
        end_date = datetime(target_year + 1, 1, 1) - timedelta(seconds=1)
    else:
        end_date = datetime(target_year, target_month + 1, 1) - timedelta(seconds=1)

    print(f"[API] Date range: {start_date} to {end_date}")

    # Get attendance records for the month
    cursor = attendance_collection.find({
        "userId": current_user.id,
        "date": {"$gte": start_date, "$lte": end_date}
    }).sort("date", -1)

    records = []
    total_days = 0
    present_days = 0
    absent_days = 0
    late_days = 0
    total_minutes = 0
    overtime_minutes = 0

    async for record in cursor:
        # Ensure proper date formatting for frontend
        attendance_record = AttendanceRecord(
            id=str(record["_id"]),
            userId=str(record["userId"]),
            date=record["date"],
            clockIn=record.get("clockIn"),
            clockOut=record.get("clockOut"),
            totalTime=record.get("totalTime", 0),
            breakTime=record.get("breakTime", 0),
            overtimeHours=record.get("overtimeHours", 0),
            status=record.get("status", "absent"),
            location=record.get("location"),
            notes=record.get("notes"),
            isManualEntry=record.get("isManualEntry", False),
            createdAt=record["createdAt"],
            updatedAt=record["updatedAt"]
        )
        records.append(attendance_record)

        # Calculate stats
        total_days += 1
        if record.get("status") == "present":
            present_days += 1
        elif record.get("status") == "late":
            late_days += 1
        else:
            absent_days += 1

        total_minutes += record.get("totalTime", 0)
        overtime_minutes += record.get("overtimeHours", 0)

    # Calculate average hours - ensure no negative values
    if total_days > 0 and total_minutes >= 0:
        average_hours = (total_minutes / 60) / total_days
        total_hours = int(total_minutes / 60)
    else:
        average_hours = 0.0
        total_hours = 0

    # Ensure overtime is not negative
    overtime_hours = max(0, int(overtime_minutes / 60))

    stats = AttendanceStats(
        totalDays=max(0, total_days),
        presentDays=max(0, present_days),
        absentDays=max(0, absent_days),
        lateDays=max(0, late_days),
        totalHours=max(0, total_hours),
        averageHours=max(0.0, round(average_hours, 2)),
        overtimeHours=overtime_hours
    )

    print(f"[API] Found {len(records)} attendance records")
    print(f"[API] Raw stats - total_minutes: {total_minutes}, total_days: {total_days}, overtime_minutes: {overtime_minutes}")
    print(f"[API] Calculated stats: {stats}")

    return AttendanceHistory(
        records=records,
        stats=stats,
        month=target_month,
        year=target_year
    )


@router.get("/stats", response_model=AttendanceStats)
async def get_attendance_stats(
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Get attendance statistics for current month"""
    # Use the history endpoint to get current month stats
    history = await get_attendance_history(None, None, current_user)
    return history.stats
