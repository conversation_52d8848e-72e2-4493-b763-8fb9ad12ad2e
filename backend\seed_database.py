#!/usr/bin/env python3
"""
Database seeding script for the attendance app.
This script populates the database with initial test data.
"""

import asyncio
from datetime import datetime, timedelta
from motor.motor_asyncio import AsyncIOMotorClient
from passlib.context import CryptContext
from bson import ObjectId

from app.config import settings

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Sample data based on your JSON examples
SAMPLE_USERS = [
    {
        "_id": ObjectId("651a8f6d9c7e4c8d3b4a5f6e"),
        "email": "<EMAIL>",
        "password": "admin123",  # Will be hashed
        "firstName": "Admin",
        "lastName": "User",
        "role": "admin",
        "department": "Operations",
        "position": "System Administrator",
        "phone": "+33123456789",
        "isActive": True,
        "profilePicture": None,
        "workSchedule": {
            "monday": {"start": "09:00", "end": "17:00", "isWorkingDay": True},
            "tuesday": {"start": "09:00", "end": "17:00", "isWorkingDay": True},
            "wednesday": {"start": "09:00", "end": "17:00", "isWorkingDay": True},
            "thursday": {"start": "09:00", "end": "17:00", "isWorkingDay": True},
            "friday": {"start": "09:00", "end": "17:00", "isWorkingDay": True},
            "saturday": {"start": "09:00", "end": "12:00", "isWorkingDay": False},
            "sunday": {"start": "09:00", "end": "12:00", "isWorkingDay": False}
        }
    },
    {
        "_id": ObjectId("651a8f6d9c7e4c8d3b4a5f6f"),
        "email": "<EMAIL>",
        "password": "manager123",  # Will be hashed
        "firstName": "Marie",
        "lastName": "Dubois",
        "role": "manager",
        "department": "Engineering",
        "position": "Engineering Manager",
        "phone": "+33123456790",
        "isActive": True,
        "profilePicture": None,
        "workSchedule": {
            "monday": {"start": "08:30", "end": "17:30", "isWorkingDay": True},
            "tuesday": {"start": "08:30", "end": "17:30", "isWorkingDay": True},
            "wednesday": {"start": "08:30", "end": "17:30", "isWorkingDay": True},
            "thursday": {"start": "08:30", "end": "17:30", "isWorkingDay": True},
            "friday": {"start": "08:30", "end": "17:30", "isWorkingDay": True},
            "saturday": {"start": "09:00", "end": "12:00", "isWorkingDay": False},
            "sunday": {"start": "09:00", "end": "12:00", "isWorkingDay": False}
        }
    },
    {
        "_id": ObjectId("651a8f6d9c7e4c8d3b4a5f70"),
        "email": "<EMAIL>",
        "password": "employee123",  # Will be hashed
        "firstName": "Pierre",
        "lastName": "Martin1",
        "role": "employee",
        "department": "Engineering",
        "position": "Software Engineer",
        "phone": "+33123456791",
        "isActive": True,
        "profilePicture": None,
        "workSchedule": {
            "monday": {"start": "09:00", "end": "17:00", "isWorkingDay": True},
            "tuesday": {"start": "09:00", "end": "17:00", "isWorkingDay": True},
            "wednesday": {"start": "09:00", "end": "17:00", "isWorkingDay": True},
            "thursday": {"start": "09:00", "end": "17:00", "isWorkingDay": True},
            "friday": {"start": "09:00", "end": "17:00", "isWorkingDay": True},
            "saturday": {"start": "09:00", "end": "12:00", "isWorkingDay": False},
            "sunday": {"start": "09:00", "end": "12:00", "isWorkingDay": False}
        }
    }
]

SAMPLE_ATTENDANCE = [
    {
        "_id": ObjectId("651a8f6d9c7e4c8d3b4a5f71"),
        "userId": ObjectId("651a8f6d9c7e4c8d3b4a5f70"),
        "date": datetime(2023, 10, 27),
        "clockIn": datetime(2023, 10, 27, 8, 55),
        "clockOut": datetime(2023, 10, 27, 17, 5),
        "totalTime": 430,  # 7h 10min
        "breakTime": 60,
        "overtimeHours": 10,
        "status": "present",
        "location": {
            "latitude": 48.8568,
            "longitude": 2.3524,
            "address": "123 Rue de la Paix, 75001 Paris, France"
        },
        "notes": None,
        "isManualEntry": False
    },
    {
        "_id": ObjectId("651a8f6d9c7e4c8d3b4a5f72"),
        "userId": ObjectId("651a8f6d9c7e4c8d3b4a5f70"),
        "date": datetime(2023, 10, 26),
        "clockIn": None,
        "clockOut": None,
        "totalTime": 0,
        "breakTime": 0,
        "overtimeHours": 0,
        "status": "absent",
        "location": None,
        "notes": "Sick day",
        "isManualEntry": True
    }
]

SAMPLE_LEAVE_REQUESTS = [
    {
        "_id": ObjectId("651a8f6d9c7e4c8d3b4a5f73"),
        "userId": ObjectId("651a8f6d9c7e4c8d3b4a5f70"),
        "type": "vacation",
        "startDate": datetime(2024, 7, 1),
        "endDate": datetime(2024, 7, 5),
        "totalDays": 5,
        "reason": "Summer holidays",
        "comment": "Visiting family abroad.",
        "status": "approved",
        "approvedBy": ObjectId("651a8f6d9c7e4c8d3b4a5f6f"),
        "approvedAt": datetime(2024, 3, 15, 14, 30),
        "rejectionReason": None,
        "documents": []
    },
    {
        "_id": ObjectId("651a8f6d9c7e4c8d3b4a5f74"),
        "userId": ObjectId("651a8f6d9c7e4c8d3b4a5f70"),
        "type": "sick",
        "startDate": datetime(2024, 4, 20),
        "endDate": datetime(2024, 4, 20),
        "totalDays": 1,
        "reason": "Flu symptoms",
        "comment": None,
        "status": "pending",
        "approvedBy": None,
        "approvedAt": None,
        "rejectionReason": None,
        "documents": []
    },
    {
        "_id": ObjectId("651a8f6d9c7e4c8d3b4a5f75"),
        "userId": ObjectId("651a8f6d9c7e4c8d3b4a5f70"),
        "type": "personal",
        "startDate": datetime(2023, 9, 1),
        "endDate": datetime(2023, 9, 2),
        "totalDays": 2,
        "reason": "Personal matters",
        "comment": None,
        "status": "rejected",
        "approvedBy": ObjectId("651a8f6d9c7e4c8d3b4a5f6f"),
        "approvedAt": datetime(2023, 8, 25, 11, 0),
        "rejectionReason": "Insufficient notice period",
        "documents": []
    }
]


async def seed_database():
    """Seed the database with sample data"""
    print("🌱 Starting database seeding...")
    
    # Connect to MongoDB
    client = AsyncIOMotorClient(settings.mongodb_uri)
    db = client[settings.mongodb_db_name]
    
    try:
        # Test connection
        await client.admin.command('ping')
        print("✅ Connected to MongoDB")
        
        # Clear existing data (optional - comment out if you want to keep existing data)
        print("🗑️  Clearing existing data...")
        await db.users.delete_many({})
        await db.attendances.delete_many({})
        await db.leaverequests.delete_many({})
        
        # Seed users
        print("👥 Seeding users...")
        users_to_insert = []
        for user in SAMPLE_USERS:
            user_copy = user.copy()
            # Hash the password
            user_copy["password"] = pwd_context.hash(user_copy["password"])
            # Add timestamps
            now = datetime.utcnow()
            user_copy["hireDate"] = datetime(2020, 1, 15) if user_copy["role"] == "admin" else datetime(2021, 3, 10) if user_copy["role"] == "manager" else datetime(2022, 5, 20)
            user_copy["createdAt"] = now
            user_copy["updatedAt"] = now
            users_to_insert.append(user_copy)
        
        await db.users.insert_many(users_to_insert)
        print(f"✅ Inserted {len(users_to_insert)} users")
        
        # Seed attendance records
        print("📅 Seeding attendance records...")
        attendance_to_insert = []
        for attendance in SAMPLE_ATTENDANCE:
            attendance_copy = attendance.copy()
            # Add timestamps
            now = datetime.utcnow()
            attendance_copy["createdAt"] = attendance_copy["date"]
            attendance_copy["updatedAt"] = now
            attendance_to_insert.append(attendance_copy)
        
        await db.attendances.insert_many(attendance_to_insert)
        print(f"✅ Inserted {len(attendance_to_insert)} attendance records")
        
        # Seed leave requests
        print("🏖️  Seeding leave requests...")
        leave_requests_to_insert = []
        for leave_request in SAMPLE_LEAVE_REQUESTS:
            leave_request_copy = leave_request.copy()
            # Add timestamps
            now = datetime.utcnow()
            leave_request_copy["createdAt"] = datetime(2024, 3, 10, 9, 0) if leave_request_copy["status"] == "approved" else datetime(2024, 4, 19, 8, 0) if leave_request_copy["status"] == "pending" else datetime(2023, 8, 20, 16, 0)
            leave_request_copy["updatedAt"] = leave_request_copy.get("approvedAt", now)
            leave_requests_to_insert.append(leave_request_copy)
        
        await db.leaverequests.insert_many(leave_requests_to_insert)
        print(f"✅ Inserted {len(leave_requests_to_insert)} leave requests")
        
        # Create indexes
        print("🔍 Creating database indexes...")
        await db.users.create_index("email", unique=True)
        await db.users.create_index("role")
        await db.attendances.create_index([("userId", 1), ("date", -1)])
        await db.leaverequests.create_index([("userId", 1), ("createdAt", -1)])
        print("✅ Created database indexes")
        
        print("\n🎉 Database seeding completed successfully!")
        print("\n📋 Test Accounts Created:")
        print("   Admin: <EMAIL> / admin123")
        print("   Manager: <EMAIL> / manager123") 
        print("   Employee: <EMAIL> / employee123")
        print("\n🚀 You can now start the FastAPI server and test the endpoints!")
        
    except Exception as e:
        print(f"❌ Error seeding database: {e}")
        raise
    finally:
        client.close()


if __name__ == "__main__":
    asyncio.run(seed_database())
