import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/store';
import {
  startTimer,
  pauseTimer,
  selectIsTimerRunning,
  selectCurrentElapsedTime,
} from '@/store/slices/timerSlice';
import { useGetAttendanceStatusQuery } from '@/store/api/apiSlice';

/**
 * Custom hook to manage attendance timer integration
 * Automatically syncs timer state with attendance status
 */
export const useAttendanceTimer = () => {
  const dispatch = useAppDispatch();
  const isTimerRunning = useAppSelector(selectIsTimerRunning);
  const currentElapsedTime = useAppSelector(selectCurrentElapsedTime);

  const {
    data: attendanceStatus,
    isLoading: isLoadingStatus,
    refetch,
  } = useGetAttendanceStatusQuery(undefined, {
    pollingInterval: 30000, // Refetch every 30 seconds
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  // Auto-sync timer with attendance status
  useEffect(() => {
    if (!isLoadingStatus && attendanceStatus) {
      const isClockedIn = attendanceStatus.isClockedIn;

      // Only pause timer if clocked out but timer is running
      // Do NOT automatically start timer when clocked in - let user control this
      if (!isClockedIn && isTimerRunning) {
        console.log('[AttendanceTimer] Auto-pausing timer - user clocked out');
        dispatch(pauseTimer());
      }
    }
  }, [attendanceStatus, isLoadingStatus, isTimerRunning, dispatch]);

  // Timer control functions
  const startTimerManually = () => {
    console.log('[AttendanceTimer] Starting timer manually');
    dispatch(startTimer());
  };

  const pauseTimerManually = () => {
    console.log('[AttendanceTimer] Pausing timer manually');
    dispatch(pauseTimer());
  };

  const getCurrentStatus = () => {
    if (!attendanceStatus) return 'clocked_out';
    if (attendanceStatus.isClockedIn && !attendanceStatus.clockOutTime)
      return 'clocked_in';
    if (attendanceStatus.clockInTime && attendanceStatus.clockOutTime)
      return 'completed';
    return 'clocked_out';
  };

  const getStatusDetails = () => {
    const currentStatus = getCurrentStatus();

    switch (currentStatus) {
      case 'clocked_in':
        return {
          text: 'Pointé',
          color: '#22c55e',
          status: 'clocked_in' as const,
        };
      case 'completed':
        return {
          text: 'Terminée',
          color: '#3b82f6',
          status: 'completed' as const,
        };
      default:
        return {
          text: 'Prêt ',
          color: '#6b7280',
          status: 'clocked_out' as const,
        };
    }
  };

  const formatDuration = (totalSeconds: number): string => {
    if (totalSeconds < 0) totalSeconds = 0;
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = Math.floor(totalSeconds % 60);

    return `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Create a todayRecord-like object for backward compatibility
  const todayRecord = attendanceStatus
    ? {
        clockIn: attendanceStatus.clockInTime,
        clockOut: attendanceStatus.clockOutTime,
        totalTime: attendanceStatus.totalTime,
        status: attendanceStatus.status,
      }
    : null;

  return {
    // Data
    todayRecord,
    attendanceStatus,
    isLoadingStatus,
    currentElapsedTime,
    isTimerRunning,

    // Computed values
    currentStatus: getCurrentStatus(),
    statusDetails: getStatusDetails(),

    // Timer controls
    startTimerManually,
    pauseTimerManually,

    // Utilities
    formatDuration,
    refetch,
  };
};
