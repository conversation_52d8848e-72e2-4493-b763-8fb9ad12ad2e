from datetime import datetime, timedelta
from typing import List, Dict, Any
from fastapi import APIRouter, Depends
from bson import ObjectId

from ..models.user import UserInDB
from ..auth import require_manager_or_admin
from ..database import get_users_collection, get_attendance_collection, get_leave_requests_collection

router = APIRouter()


@router.get("/stats")
async def get_manager_stats(
    current_user: UserInDB = Depends(require_manager_or_admin)
) -> Dict[str, Any]:
    """Get manager dashboard statistics"""
    users_collection = get_users_collection()
    attendance_collection = get_attendance_collection()
    leave_requests_collection = get_leave_requests_collection()
    
    # Get today's date
    today = datetime.utcnow().date()
    start_of_day = datetime.combine(today, datetime.min.time())
    end_of_day = datetime.combine(today, datetime.max.time())
    
    # Get week start (Monday)
    days_since_monday = today.weekday()
    week_start = today - timedelta(days=days_since_monday)
    week_start_datetime = datetime.combine(week_start, datetime.min.time())
    
    # Get all active employees
    total_employees = await users_collection.count_documents({
        "isActive": True,
        "role": "employee"
    })
    
    # Get employees on leave today
    employees_on_leave = await leave_requests_collection.count_documents({
        "status": "approved",
        "startDate": {"$lte": end_of_day},
        "endDate": {"$gte": start_of_day}
    })
    
    # Get today's attendance records
    today_attendance = []
    cursor = attendance_collection.find({
        "date": {"$gte": start_of_day, "$lte": end_of_day}
    })
    
    present_count = 0
    async for record in cursor:
        if record.get("status") in ["present", "late"]:
            present_count += 1
    
    # Calculate absent employees (total - present - on leave)
    absent_count = max(0, total_employees - present_count - employees_on_leave)
    
    # Get weekly attendance for average calculation
    weekly_attendance = []
    weekly_cursor = attendance_collection.find({
        "date": {"$gte": week_start_datetime, "$lte": end_of_day}
    })
    
    total_weekly_minutes = 0
    weekly_records_count = 0
    
    async for record in weekly_cursor:
        if record.get("totalTime"):
            total_weekly_minutes += record["totalTime"]
            weekly_records_count += 1
    
    # Calculate average hours worked this week
    if weekly_records_count > 0:
        avg_minutes = total_weekly_minutes / weekly_records_count
        avg_hours = int(avg_minutes // 60)
        avg_mins = int(avg_minutes % 60)
        avg_hours_worked = f"{avg_hours}h {avg_mins}min"
    else:
        avg_hours_worked = "0h 0min"
    
    # Calculate attendance rate for the week
    expected_attendance = total_employees * (days_since_monday + 1)  # Days elapsed this week
    actual_attendance = weekly_records_count
    attendance_rate = int((actual_attendance / max(1, expected_attendance)) * 100)
    
    # Get pending leave requests count
    pending_requests = await leave_requests_collection.count_documents({
        "status": "pending"
    })
    
    return {
        "todayStats": {
            "totalEmployees": total_employees,
            "onLeave": employees_on_leave,
            "absent": absent_count,
            "present": present_count
        },
        "weeklyStats": {
            "attendanceRate": attendance_rate,
            "avgHoursWorked": avg_hours_worked
        },
        "pendingRequests": pending_requests
    }


@router.get("/team-absences")
async def get_team_absences(
    current_user: UserInDB = Depends(require_manager_or_admin)
) -> Dict[str, Any]:
    """Get team members who are absent or on leave today"""
    users_collection = get_users_collection()
    attendance_collection = get_attendance_collection()
    leave_requests_collection = get_leave_requests_collection()

    # Get today's date
    today = datetime.utcnow().date()
    start_of_day = datetime.combine(today, datetime.min.time())
    end_of_day = datetime.combine(today, datetime.max.time())

    # Get all active employees
    employees_cursor = users_collection.find({
        "isActive": True,
        "role": "employee"
    })

    on_leave = []
    absent = []

    async for employee in employees_cursor:
        employee_id = employee["_id"]

        # Check if employee has attendance record today
        attendance_record = await attendance_collection.find_one({
            "userId": employee_id,
            "date": {"$gte": start_of_day, "$lte": end_of_day}
        })

        # Check if employee is on approved leave today
        leave_record = await leave_requests_collection.find_one({
            "userId": employee_id,
            "status": "approved",
            "startDate": {"$lte": end_of_day},
            "endDate": {"$gte": start_of_day}
        })

        # Create employee info with full name
        employee_info = {
            "id": str(employee_id),
            "name": f"{employee['firstName']} {employee['lastName']}",
            "email": employee["email"],
            "department": employee.get("department", ""),
            "position": employee.get("position", "")
        }

        # Determine status and add to appropriate list
        if leave_record:
            leave_info = employee_info.copy()
            leave_info.update({
                "type": leave_record['type'].title(),
                "endDate": leave_record['endDate'].isoformat() if isinstance(leave_record['endDate'], datetime) else str(leave_record['endDate']),
                "reason": leave_record.get('reason', f"{leave_record['type'].title()} Leave")
            })
            on_leave.append(leave_info)
        elif not attendance_record or not attendance_record.get("clockIn"):
            absent_info = employee_info.copy()
            absent_info["reason"] = "Non pointé"
            absent.append(absent_info)
        # If employee is present, we skip them (don't add to any list)

    return {
        "onLeave": on_leave,
        "absent": absent
    }


@router.get("/attendance-overview")
async def get_attendance_overview(
    current_user: UserInDB = Depends(require_manager_or_admin)
) -> Dict[str, Any]:
    """Get attendance overview for the current month"""
    attendance_collection = get_attendance_collection()
    users_collection = get_users_collection()
    
    # Get current month date range
    now = datetime.utcnow()
    start_of_month = datetime(now.year, now.month, 1)
    if now.month == 12:
        end_of_month = datetime(now.year + 1, 1, 1) - timedelta(seconds=1)
    else:
        end_of_month = datetime(now.year, now.month + 1, 1) - timedelta(seconds=1)
    
    # Get total active employees
    total_employees = await users_collection.count_documents({
        "isActive": True,
        "role": "employee"
    })
    
    # Get attendance statistics for the month
    pipeline = [
        {
            "$match": {
                "date": {"$gte": start_of_month, "$lte": end_of_month}
            }
        },
        {
            "$group": {
                "_id": "$status",
                "count": {"$sum": 1}
            }
        }
    ]
    
    status_counts = {"present": 0, "late": 0, "absent": 0}
    async for result in attendance_collection.aggregate(pipeline):
        status = result["_id"]
        if status in status_counts:
            status_counts[status] = result["count"]
    
    # Calculate working days in the month (excluding weekends)
    current_date = start_of_month.date()
    end_date = end_of_month.date()
    working_days = 0
    
    while current_date <= end_date:
        if current_date.weekday() < 5:  # Monday to Friday
            working_days += 1
        current_date += timedelta(days=1)
    
    # Calculate expected total attendance
    expected_attendance = total_employees * working_days
    actual_attendance = sum(status_counts.values())
    
    # Calculate attendance rate
    attendance_rate = int((actual_attendance / max(1, expected_attendance)) * 100)
    
    return {
        "month": now.month,
        "year": now.year,
        "totalEmployees": total_employees,
        "workingDays": working_days,
        "attendanceRate": attendance_rate,
        "statusBreakdown": status_counts,
        "expectedAttendance": expected_attendance,
        "actualAttendance": actual_attendance
    }
