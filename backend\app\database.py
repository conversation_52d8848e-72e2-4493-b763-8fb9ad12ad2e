from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import ConnectionFailure
import logging
from .config import settings

logger = logging.getLogger(__name__)

# Global variables for database connection
client: AsyncIOMotorClient = None
database: AsyncIOMotorDatabase = None


async def connect_to_mongo():
    """Create database connection"""
    global client, database
    try:
        client = AsyncIOMotorClient(settings.mongodb_uri)
        database = client[settings.mongodb_db_name]
        
        # Test the connection
        await client.admin.command('ping')
        logger.info(f"Connected to MongoDB: {settings.mongodb_db_name}")
        
        # Create indexes
        await create_indexes()
        
    except ConnectionFailure as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        raise e


async def close_mongo_connection():
    """Close database connection"""
    global client
    if client:
        client.close()
        logger.info("Disconnected from MongoDB")


async def get_database() -> AsyncIOMotorDatabase:
    """Get database instance"""
    return database


async def create_indexes():
    """Create database indexes for better performance"""
    try:
        # Users collection indexes
        await database.users.create_index("email", unique=True)
        await database.users.create_index("role")
        await database.users.create_index("department")
        await database.users.create_index("isActive")
        
        # Attendance collection indexes
        await database.attendances.create_index([("userId", 1), ("date", -1)])
        await database.attendances.create_index("date")
        await database.attendances.create_index("status")
        
        # Leave requests collection indexes
        await database.leaverequests.create_index([("userId", 1), ("createdAt", -1)])
        await database.leaverequests.create_index("status")
        await database.leaverequests.create_index([("startDate", 1), ("endDate", 1)])
        await database.leaverequests.create_index("approvedBy")
        
        logger.info("Database indexes created successfully")
        
    except Exception as e:
        logger.error(f"Failed to create indexes: {e}")


# Collection getters
def get_users_collection():
    return database.users


def get_attendance_collection():
    return database.attendances


def get_leave_requests_collection():
    return database.leaverequests
