import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useState } from 'react';
import { ScreenWrapper } from '@/components/ui/ScreenWrapper';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Modal } from '@/components/ui/Modal';
import { InputField } from '@/components/ui/InputField';
import {
  useGetLeaveRequestsQuery,
  useCreateLeaveRequestMutation,
} from '@/store/api/apiSlice';
import { Plus, Calendar, MessageCircle } from 'lucide-react-native';

export default function LeaveScreen() {
  const [showModal, setShowModal] = useState(false);
  const [activeTab, setActiveTab] = useState('pending');
  const [formData, setFormData] = useState({
    type: 'vacation' as
      | 'vacation'
      | 'sick'
      | 'personal'
      | 'maternity'
      | 'paternity'
      | 'other',
    startDate: '',
    endDate: '',
    reason: '',
    comment: '',
  });

  // RTK Query hooks
  const {
    data: leaveData,
    isLoading: isLoadingRequests,
    error: requestsError,
    refetch,
  } = useGetLeaveRequestsQuery({ status: activeTab });

  const [createRequest, { isLoading: isCreatingRequest }] =
    useCreateLeaveRequestMutation();

  const requests = leaveData?.data || [];

  // Helper function to calculate business days
  const calculateBusinessDays = (
    startDate: string,
    endDate: string
  ): number => {
    const start = new Date(startDate.split('/').reverse().join('-')); // Convert DD/MM/YYYY to YYYY-MM-DD
    const end = new Date(endDate.split('/').reverse().join('-'));

    let businessDays = 0;
    const currentDate = new Date(start);

    while (currentDate <= end) {
      const dayOfWeek = currentDate.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        // Not Sunday (0) or Saturday (6)
        businessDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return businessDays;
  };

  // Helper function to convert DD/MM/YYYY to ISO date string
  const convertToISODate = (dateString: string): string => {
    const [day, month, year] = dateString.split('/');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(
      2,
      '0'
    )}T00:00:00.000Z`;
  };

  const tabs = [
    { id: 'pending', label: 'En attente', color: '#F59E0B' },
    { id: 'approved', label: 'Validées', color: '#10B981' },
    { id: 'rejected', label: 'Refusées', color: '#EF4444' },
  ];

  const handleSubmit = async () => {
    if (!formData.startDate || !formData.endDate || !formData.reason) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs obligatoires');
      return;
    }

    // Validate date format (DD/MM/YYYY)
    const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
    if (
      !dateRegex.test(formData.startDate) ||
      !dateRegex.test(formData.endDate)
    ) {
      Alert.alert('Erreur', 'Format de date invalide. Utilisez DD/MM/YYYY');
      return;
    }

    try {
      // Calculate business days
      const totalDays = calculateBusinessDays(
        formData.startDate,
        formData.endDate
      );

      if (totalDays <= 0) {
        Alert.alert(
          'Erreur',
          'La date de fin doit être après la date de début'
        );
        return;
      }

      await createRequest({
        type: formData.type,
        startDate: convertToISODate(formData.startDate),
        endDate: convertToISODate(formData.endDate),
        totalDays: totalDays,
        reason: formData.reason,
        comment: formData.comment || undefined,
        documents: [],
      }).unwrap();

      setShowModal(false);
      setFormData({
        type: 'vacation',
        startDate: '',
        endDate: '',
        reason: '',
        comment: '',
      });

      Alert.alert('Succès', 'Demande de congé créée avec succès');
    } catch (error: any) {
      console.error('Erreur lors de la création:', error);
      Alert.alert(
        'Erreur',
        error?.data?.detail ||
          error?.data?.message ||
          'Impossible de créer la demande'
      );
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'approved':
        return 'success';
      case 'rejected':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  const renderLeaveRequest = ({ item }: { item: any }) => (
    <Card style={styles.requestCard}>
      <View style={styles.requestHeader}>
        <Text style={styles.requestType}>{item.type}</Text>
        <Badge
          variant={getStatusBadgeVariant(item.status)}
          text={item.status}
        />
      </View>
      <View style={styles.requestDates}>
        <Calendar size={16} color="#6b7280" />
        <Text style={styles.datesText}>
          Du {item.startDate} au {item.endDate}
        </Text>
      </View>
      {item.comment && (
        <View style={styles.requestComment}>
          <MessageCircle size={16} color="#6b7280" />
          <Text style={styles.commentText}>{item.comment}</Text>
        </View>
      )}
    </Card>
  );

  return (
    <ScreenWrapper>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Mes Congés</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowModal(true)}
          >
            <Plus size={24} color="#ffffff" />
          </TouchableOpacity>
        </View>

        <View style={styles.tabs}>
          {tabs.map((tab) => (
            <TouchableOpacity
              key={tab.id}
              style={[
                styles.tab,
                activeTab === tab.id && { borderBottomColor: tab.color },
              ]}
              onPress={() => setActiveTab(tab.id)}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === tab.id && { color: tab.color },
                ]}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {isLoadingRequests ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#3b82f6" />
            <Text style={styles.loadingText}>Chargement des demandes...</Text>
          </View>
        ) : (
          <FlatList
            data={requests}
            renderItem={renderLeaveRequest}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>
                  Aucune demande{' '}
                  {activeTab === 'pending'
                    ? 'en attente'
                    : activeTab === 'approved'
                    ? 'validée'
                    : 'refusée'}
                </Text>
              </View>
            }
          />
        )}

        <Modal
          visible={showModal}
          onClose={() => setShowModal(false)}
          title="Nouvelle demande de congé"
        >
          <View style={styles.modalContent}>
            <InputField
              label="Type de congé"
              value={formData.type}
              onChangeText={(text) =>
                setFormData({
                  ...formData,
                  type: text as
                    | 'vacation'
                    | 'sick'
                    | 'personal'
                    | 'maternity'
                    | 'paternity'
                    | 'other',
                })
              }
              placeholder="vacation | sick | personal | maternity | paternity | other"
            />
            <InputField
              label="Date de début"
              value={formData.startDate}
              onChangeText={(text) =>
                setFormData({ ...formData, startDate: text })
              }
              placeholder="JJ/MM/AAAA"
            />
            <InputField
              label="Date de fin"
              value={formData.endDate}
              onChangeText={(text) =>
                setFormData({ ...formData, endDate: text })
              }
              placeholder="JJ/MM/AAAA"
            />
            <InputField
              label="Raison *"
              value={formData.reason}
              onChangeText={(text) =>
                setFormData({ ...formData, reason: text })
              }
              placeholder="Motif de la demande..."
              multiline
            />
            <InputField
              label="Commentaire (optionnel)"
              value={formData.comment}
              onChangeText={(text) =>
                setFormData({ ...formData, comment: text })
              }
              placeholder="Informations supplémentaires..."
              multiline
            />
            <Button
              title="Soumettre la demande"
              onPress={handleSubmit}
              loading={isCreatingRequest}
              style={styles.submitButton}
            />
          </View>
        </Modal>
      </View>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#111827',
  },
  addButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#3B82F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6b7280',
  },
  listContainer: {
    padding: 16,
  },
  requestCard: {
    marginBottom: 12,
  },
  requestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  requestType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  requestDates: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  datesText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#6b7280',
  },
  requestComment: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  commentText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#6b7280',
    flex: 1,
  },
  modalContent: {
    gap: 16,
  },
  submitButton: {
    marginTop: 8,
    marginBottom: 30,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
});
