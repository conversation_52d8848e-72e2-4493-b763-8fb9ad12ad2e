# FastAPI Attendance Backend

This is the FastAPI backend for the Expo attendance tracking app.

## Features

- **Authentication**: JWT-based authentication with password hashing
- **User Management**: CRUD operations for users with role-based access
- **Attendance Tracking**: Clock-in/out functionality with location tracking
- **Leave Management**: Leave request creation, approval, and balance tracking
- **Manager Dashboard**: Statistics and team management features
- **MongoDB Integration**: Async MongoDB operations with Motor

## Setup

### 1. Install Dependencies

```bash
cd backend
pip install -r requirements.txt
```

### 2. Environment Configuration

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` with your MongoDB connection string and other settings:

```env
MONGODB_URI=mongodb://localhost:27017/attendance_app
MONGODB_DB_NAME=attendance_app
SECRET_KEY=your-super-secret-key-change-this-in-production
```

### 3. Start MongoDB

Make sure MongoDB is running on your system:

```bash
# Using Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest

# Or using local MongoDB installation
mongod
```

### 4. Run the FastAPI Server

```bash
# Development mode with auto-reload
python main.py

# Or using uvicorn directly
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

The API will be available at:

- **API**: http://localhost:8000
- **Interactive Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Features

- **Authentication**: JWT-based authentication with password hashing
- **User Management**: CRUD operations for users with role-based access
- **Attendance Tracking**: Clock-in/out functionality with location tracking
- **Leave Management**: Leave request creation, approval, and balance tracking
- **Manager Dashboard**: Statistics and team management features
- **MongoDB Integration**: Async MongoDB operations with Motor

## Setup

### 1. Install Dependencies

```bash
cd backend
pip install -r requirements.txt
```

### 2. Environment Configuration

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` with your MongoDB connection string and other settings:

```env
MONGODB_URI=mongodb://localhost:27017/attendance_app
MONGODB_DB_NAME=attendance_app
SECRET_KEY=your-super-secret-key-change-this-in-production
```

### 3. Start MongoDB

Make sure MongoDB is running on your system:

```bash
# Using Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest

# Or using local MongoDB installation
mongod
```

### 4. Run the FastAPI Server

```bash
# Development mode with auto-reload
python main.py

# Or using uvicorn directly
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

The API will be available at:

- **API**: http://localhost:8000
- **Interactive Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## API Endpoints

### Authentication

- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user profile
- `POST /api/auth/change-password` - Change password

### Users

- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `GET /api/users/` - Get all users (admin only)
- `POST /api/users/` - Create user (admin only)
- `PUT /api/users/{user_id}` - Update user (admin only)
- `DELETE /api/users/{user_id}` - Delete user (admin only)

### Attendance

- `GET /api/attendance/status` - Get attendance status
- `POST /api/attendance/clock-in` - Clock in
- `POST /api/attendance/clock-out` - Clock out
- `GET /api/attendance/history` - Get attendance history
- `GET /api/attendance/stats` - Get attendance statistics

### Leave Requests

- `GET /api/leave/` - Get user's leave requests
- `POST /api/leave/` - Create leave request
- `PUT /api/leave/{request_id}` - Update leave request
- `DELETE /api/leave/{request_id}` - Cancel leave request
- `GET /api/leave/balance` - Get leave balance
- `GET /api/leave/pending` - Get pending requests (manager/admin)
- `POST /api/leave/{request_id}/approve` - Approve/reject request (manager/admin)

### Manager Dashboard

- `GET /api/manager/stats` - Get dashboard statistics
- `GET /api/manager/team-absences` - Get team absences
- `GET /api/manager/attendance-overview` - Get attendance overview

## Database Schema

The application uses MongoDB with the following collections:

### Users Collection

```json
{
  "_id": ObjectId,
  "email": "<EMAIL>",
  "password": "hashed_password",
  "firstName": "John",
  "lastName": "Doe",
  "role": "employee|manager|admin",
  "department": "Engineering",
  "position": "Software Engineer",
  "phone": "+1234567890",
  "isActive": true,
  "profilePicture": "url",
  "workSchedule": {...},
  "hireDate": ISODate,
  "createdAt": ISODate,
  "updatedAt": ISODate
}
```

### Attendance Collection

```json
{
  "_id": ObjectId,
  "userId": ObjectId,
  "date": ISODate,
  "clockIn": ISODate,
  "clockOut": ISODate,
  "totalTime": 480,
  "breakTime": 60,
  "overtimeHours": 0,
  "status": "present|late|absent",
  "location": {...},
  "notes": "string",
  "isManualEntry": false,
  "createdAt": ISODate,
  "updatedAt": ISODate
}
```

### Leave Requests Collection

```json
{
  "_id": ObjectId,
  "userId": ObjectId,
  "type": "vacation|sick|personal",
  "startDate": ISODate,
  "endDate": ISODate,
  "totalDays": 5,
  "reason": "string",
  "comment": "string",
  "status": "pending|approved|rejected",
  "approvedBy": ObjectId,
  "approvedAt": ISODate,
  "rejectionReason": "string",
  "documents": [],
  "createdAt": ISODate,
  "updatedAt": ISODate
}
```

## Development

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest
```

### Code Formatting

```bash
# Install formatting tools
pip install black isort

# Format code
black .
isort .
```

## Deployment

### Using Docker

```bash
# Build image
docker build -t attendance-api .

# Run container
docker run -d -p 8000:8000 --env-file .env attendance-api
```

### Environment Variables for Production

- Set `DEBUG=False`
- Use a strong `SECRET_KEY`
- Configure `ALLOWED_ORIGINS` for your frontend domain
- Use a production MongoDB instance

## Integration with Expo App

The Expo app is configured to connect to this FastAPI backend. Make sure to:

1. Update the `EXPO_PUBLIC_API_URL` in your Expo app's `.env` file
2. Ensure CORS is properly configured for your Expo app's origin
3. The API endpoints match the RTK Query definitions in the Expo app

## Security Features

- JWT token authentication
- Password hashing with bcrypt
- Role-based access control
- Input validation with Pydantic
- CORS protection
- MongoDB injection protection
