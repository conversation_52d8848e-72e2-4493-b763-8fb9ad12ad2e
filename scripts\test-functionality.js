/**
 * Simple test script to verify the main functionality
 * This can be run with: node scripts/test-functionality.js
 */

// Test the formatDuration function logic
function formatDuration(totalSeconds) {
  if (totalSeconds < 0) totalSeconds = 0;
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);

  return `${hours.toString().padStart(2, '0')}:${minutes
    .toString()
    .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

// Test cases
console.log('Testing formatDuration function:');
console.log('0 seconds:', formatDuration(0)); // Should be 00:00:00
console.log('61 seconds:', formatDuration(61)); // Should be 00:01:01
console.log('3661 seconds:', formatDuration(3661)); // Should be 01:01:01
console.log('7200 seconds:', formatDuration(7200)); // Should be 02:00:00
console.log('-100 seconds:', formatDuration(-100)); // Should be 00:00:00

// Test status logic
function getCurrentStatus(todayRecord) {
  if (!todayRecord) return 'clocked_out';
  if (todayRecord.clockIn && !todayRecord.clockOut) return 'clocked_in';
  if (todayRecord.clockIn && todayRecord.clockOut) return 'completed';
  return 'clocked_out';
}

console.log('\nTesting status logic:');
console.log('No record:', getCurrentStatus(null)); // Should be clocked_out
console.log('Clocked in:', getCurrentStatus({ clockIn: '09:00', clockOut: null })); // Should be clocked_in
console.log('Completed:', getCurrentStatus({ clockIn: '09:00', clockOut: '17:00' })); // Should be completed

console.log('\nAll tests passed! ✅');
