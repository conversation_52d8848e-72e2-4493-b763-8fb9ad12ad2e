# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/attendance_app
MONGODB_DB_NAME=attendance_app

# JWT Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# CORS Configuration (for Expo app)
ALLOWED_ORIGINS=["http://localhost:8081", "exp://*************:8081"]
