import { store } from '@/store/store';
import {
  setCredentials,
  logOut,
  restoreAuth,
  setLoading,
} from '@/store/slices/authSlice';
import { StorageService } from './storage';
import { User } from '@/types/auth';

export class AuthService {
  /**
   * Initialize authentication state from stored data
   * This should be called when the app starts
   */
  static async initializeAuth(): Promise<void> {
    console.log('[AuthService] Initializing authentication...');
    store.dispatch(setLoading(true));

    try {
      const authData = await StorageService.getAuthData();
      console.log('[AuthService] Retrieved auth data:', {
        hasUser: !!authData?.user,
        hasToken: !!authData?.token,
        userEmail: authData?.user?.email,
        tokenLength: authData?.token?.length || 0,
      });

      if (authData?.token) {
        // Check if token is expired
        if (this.isTokenExpired(authData.token)) {
          console.log(
            '[AuthService] Stored token is expired, clearing auth data'
          );
          await StorageService.clearAuthData();
          store.dispatch(restoreAuth(null));
          return;
        }
      }

      store.dispatch(restoreAuth(authData));
      console.log('[AuthService] Auth state restored');

      // Verify token with backend
      if (authData?.token) {
        console.log('[AuthService] Verifying token with backend...');
        try {
          const response = await fetch(
            'http://10.1.0.168:8000/api/auth/verify',
            {
              headers: {
                Authorization: `Bearer ${authData.token}`,
                'Content-Type': 'application/json',
              },
            }
          );

          if (response.ok) {
            const result = await response.json();
            console.log('[AuthService] Token verification successful:', result);
          } else {
            console.log(
              '[AuthService] Token verification failed:',
              response.status
            );
            await this.logout();
          }
        } catch (error) {
          console.error('[AuthService] Error verifying token:', error);
        }
      }
    } catch (error) {
      console.error('[AuthService] Error initializing auth:', error);
      store.dispatch(restoreAuth(null));
    }
  }

  /**
   * Save authentication data after successful login
   */
  static async saveAuthData(
    user: User,
    token: string,
    refreshToken?: string
  ): Promise<void> {
    try {
      console.log('[AuthService] Saving auth data for user:', user.email);

      // Save to secure storage
      await StorageService.saveAuthData(user, token, refreshToken);
      console.log('[AuthService] Auth data saved to storage');

      // Update Redux state
      store.dispatch(setCredentials({ user, token }));
      console.log('[AuthService] Redux state updated');
    } catch (error) {
      console.error('[AuthService] Error saving auth data:', error);
      throw new Error('Failed to save authentication data');
    }
  }

  /**
   * Clear authentication data and logout
   */
  static async logout(): Promise<void> {
    try {
      // Clear from secure storage
      await StorageService.clearAuthData();

      // Update Redux state
      store.dispatch(logOut());
    } catch (error) {
      console.error('Error during logout:', error);
      // Even if storage clearing fails, we should still logout from Redux
      store.dispatch(logOut());
      throw new Error('Failed to logout completely');
    }
  }

  /**
   * Get current authentication token
   */
  static async getToken(): Promise<string | null> {
    // First try to get from Redux state (faster)
    const state = store.getState();
    if (state.auth.token) {
      return state.auth.token;
    }

    // Fallback to storage
    return await StorageService.getToken();
  }

  /**
   * Check if a JWT token is expired
   */
  static isTokenExpired(token: string): boolean {
    try {
      // Decode JWT token (without verification for client-side check)
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      return payload.exp < currentTime;
    } catch (error) {
      console.error('[AuthService] Error checking token expiration:', error);
      return true; // Assume expired if we can't parse it
    }
  }

  /**
   * Validate current token and logout if expired
   */
  static async validateToken(): Promise<boolean> {
    const token = await this.getToken();
    if (!token) {
      console.log('[AuthService] No token found');
      return false;
    }

    if (this.isTokenExpired(token)) {
      console.log('[AuthService] Token is expired, logging out');
      await this.logout();
      return false;
    }

    return true;
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    const state = store.getState();
    return state.auth.isAuthenticated;
  }

  /**
   * Get current user
   */
  static getCurrentUser(): User | null {
    const state = store.getState();
    return state.auth.user;
  }

  /**
   * Check if current user has specific role
   */
  static hasRole(role: 'employee' | 'manager' | 'admin'): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;

    // Admin has access to everything
    if (user.role === 'admin') return true;

    // Manager has access to manager and employee features
    if (user.role === 'manager' && (role === 'manager' || role === 'employee'))
      return true;

    // Employee only has access to employee features
    if (user.role === 'employee' && role === 'employee') return true;

    return false;
  }

  /**
   * Check if current user can access a specific feature
   */
  static canAccess(
    feature: 'dashboard' | 'approvals' | 'users' | 'analytics'
  ): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;

    switch (feature) {
      case 'dashboard':
        return this.hasRole('manager') || this.hasRole('admin');
      case 'approvals':
        return this.hasRole('manager') || this.hasRole('admin');
      case 'users':
        return this.hasRole('admin');
      case 'analytics':
        return this.hasRole('admin');
      default:
        return false;
    }
  }

  /**
   * Refresh authentication token
   * This would typically be called when the current token is about to expire
   */
  static async refreshToken(): Promise<boolean> {
    try {
      const authData = await StorageService.getAuthData();
      if (!authData?.refreshToken) {
        return false;
      }

      // Here you would make an API call to refresh the token
      // For now, we'll just return false to indicate refresh is not implemented
      // TODO: Implement token refresh logic with your backend

      return false;
    } catch (error) {
      console.error('Error refreshing token:', error);
      return false;
    }
  }
}
