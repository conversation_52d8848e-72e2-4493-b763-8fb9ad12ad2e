#!/usr/bin/env python3
"""
Migration script to fix leave request records with missing updatedAt fields.
This script should be run once to update existing database records.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import connect_to_mongo, close_mongo_connection, get_leave_requests_collection


async def migrate_leave_requests():
    """Update leave request records that have null or missing updatedAt fields"""
    print("[MIGRATION] Starting leave request migration...")
    
    try:
        # Connect to database
        await connect_to_mongo()
        leave_requests_collection = get_leave_requests_collection()
        
        # Find records with null or missing updatedAt
        query = {
            "$or": [
                {"updatedAt": {"$exists": False}},
                {"updatedAt": None}
            ]
        }
        
        # Count records that need updating
        count = await leave_requests_collection.count_documents(query)
        print(f"[MIGRATION] Found {count} leave request records that need updatedAt field")
        
        if count == 0:
            print("[MIGRATION] No records need updating")
            return
        
        # Update records - set updatedAt to createdAt for existing records
        update_result = await leave_requests_collection.update_many(
            query,
            [
                {
                    "$set": {
                        "updatedAt": {
                            "$cond": {
                                "if": {"$ifNull": ["$createdAt", False]},
                                "then": "$createdAt",
                                "else": datetime.utcnow()
                            }
                        }
                    }
                }
            ]
        )
        
        print(f"[MIGRATION] Updated {update_result.modified_count} leave request records")
        
        # Verify the update
        remaining_count = await leave_requests_collection.count_documents(query)
        print(f"[MIGRATION] {remaining_count} records still need updating (should be 0)")
        
        if remaining_count == 0:
            print("[MIGRATION] ✅ Migration completed successfully!")
        else:
            print("[MIGRATION] ⚠️ Some records still need updating")
            
    except Exception as e:
        print(f"[MIGRATION] ❌ Error during migration: {e}")
        raise
    finally:
        await close_mongo_connection()


async def verify_migration():
    """Verify that all leave request records have valid updatedAt fields"""
    print("[VERIFICATION] Verifying leave request records...")
    
    try:
        await connect_to_mongo()
        leave_requests_collection = get_leave_requests_collection()
        
        # Check for any records with null updatedAt
        null_count = await leave_requests_collection.count_documents({
            "$or": [
                {"updatedAt": {"$exists": False}},
                {"updatedAt": None}
            ]
        })
        
        # Get total count
        total_count = await leave_requests_collection.count_documents({})
        
        print(f"[VERIFICATION] Total leave requests: {total_count}")
        print(f"[VERIFICATION] Records with null updatedAt: {null_count}")
        
        if null_count == 0:
            print("[VERIFICATION] ✅ All records have valid updatedAt fields")
        else:
            print(f"[VERIFICATION] ⚠️ {null_count} records still have null updatedAt")
            
        return null_count == 0
        
    except Exception as e:
        print(f"[VERIFICATION] ❌ Error during verification: {e}")
        return False
    finally:
        await close_mongo_connection()


async def main():
    """Main migration function"""
    print("=" * 50)
    print("Leave Request Migration Script")
    print("=" * 50)
    
    # First verify current state
    await verify_migration()
    
    print("\n" + "=" * 50)
    
    # Run migration
    await migrate_leave_requests()
    
    print("\n" + "=" * 50)
    
    # Verify after migration
    success = await verify_migration()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Migration completed successfully!")
    else:
        print("❌ Migration completed with issues")
    print("=" * 50)


if __name__ == "__main__":
    asyncio.run(main())
