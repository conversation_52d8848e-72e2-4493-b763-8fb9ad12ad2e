from datetime import datetime, date, timedelta
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from bson import ObjectId
from pymongo.errors import PyMongoError
import logging

from ..models.leave_request import (
    LeaveRequest, LeaveRequestCreate, LeaveRequestUpdate,
    LeaveRequestWithUser, ApprovalAction, LeaveBalance
)
from ..models.user import UserInDB
from ..models.common import PaginatedResponse
from ..auth import get_current_active_user, require_manager_or_admin
from ..database import get_leave_requests_collection, get_users_collection

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/test")
async def test_leave_endpoints():
    """Test endpoint to validate leave request data structures"""
    return {
        "message": "Leave request endpoints are working",
        "endpoints": {
            "GET /leave": "Get user leave requests with pagination",
            "POST /leave": "Create new leave request",
            "GET /leave/pending": "Get pending leave requests (manager only)",
            "GET /leave/balance": "Get user leave balance"
        },
        "expected_response_format": {
            "paginated": {
                "data": "Array of leave requests",
                "pagination": {
                    "page": "Current page number",
                    "limit": "Items per page",
                    "total": "Total items",
                    "totalPages": "Total pages"
                }
            }
        }
    }


def calculate_business_days(start_date: datetime, end_date: datetime) -> int:
    """Calculate number of business days between two dates"""
    current = start_date.date()
    end = end_date.date()
    business_days = 0
    
    while current <= end:
        # Monday = 0, Sunday = 6
        if current.weekday() < 5:  # Monday to Friday
            business_days += 1
        current += timedelta(days=1)
    
    return business_days


@router.get("/", response_model=PaginatedResponse[LeaveRequest])
async def get_user_leave_requests(
    page: int = Query(1, ge=1),
    status: Optional[str] = Query(None, regex="^(pending|approved|rejected)$"),
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Get current user's leave requests with pagination"""
    try:
        print(f"[API] Getting leave requests for user {current_user.id}, page: {page}, status: {status}")

        leave_requests_collection = get_leave_requests_collection()

        # Build query
        query = {"userId": current_user.id}
        if status:
            query["status"] = status

        print(f"[API] Query: {query}")

        # Pagination settings
        limit = 10
        skip = (page - 1) * limit

        # Get total count
        total = await leave_requests_collection.count_documents(query)

        # Get leave requests with pagination
        cursor = leave_requests_collection.find(query).sort("createdAt", -1).skip(skip).limit(limit)

        requests = []
        async for request_data in cursor:
            try:
                leave_request = LeaveRequest(
                    id=str(request_data["_id"]),
                    userId=str(request_data["userId"]),
                    type=request_data["type"],
                    startDate=request_data["startDate"],
                    endDate=request_data["endDate"],
                    totalDays=request_data["totalDays"],
                    reason=request_data["reason"],
                    comment=request_data.get("comment"),
                    documents=request_data.get("documents", []),
                    status=request_data["status"],
                    approvedBy=str(request_data["approvedBy"]) if request_data.get("approvedBy") else None,
                    approvedAt=request_data.get("approvedAt"),
                    rejectionReason=request_data.get("rejectionReason"),
                    createdAt=request_data["createdAt"],
                    updatedAt=request_data.get("updatedAt")  # Handle null values
                )
                requests.append(leave_request)
            except Exception as e:
                print(f"[API] Error processing leave request {request_data.get('_id')}: {e}")
                print(f"[API] Request data: {request_data}")
                # Skip this record and continue with others
                continue

        # Calculate pagination info
        total_pages = (total + limit - 1) // limit

        print(f"[API] Found {len(requests)} leave requests, total: {total}, page: {page}/{total_pages}")

        return PaginatedResponse(
            data=requests,
            pagination={
                "page": page,
                "limit": limit,
                "total": total,
                "totalPages": total_pages
            }
        )

    except PyMongoError as e:
        logger.error(f"Database error in get_user_leave_requests: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while retrieving leave requests"
        )
    except Exception as e:
        logger.error(f"Unexpected error in get_user_leave_requests: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while retrieving leave requests"
        )


@router.post("/", response_model=LeaveRequest)
async def create_leave_request(
    request_data: LeaveRequestCreate,
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Create a new leave request"""
    leave_requests_collection = get_leave_requests_collection()
    
    # Validate dates
    if request_data.startDate >= request_data.endDate:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="End date must be after start date"
        )
    
    # Calculate business days
    business_days = calculate_business_days(request_data.startDate, request_data.endDate)
    
    # Check for overlapping requests
    existing_request = await leave_requests_collection.find_one({
        "userId": current_user.id,
        "status": {"$in": ["pending", "approved"]},
        "$or": [
            {
                "startDate": {"$lte": request_data.endDate},
                "endDate": {"$gte": request_data.startDate}
            }
        ]
    })
    
    if existing_request:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You have an overlapping leave request"
        )
    
    # Create leave request document
    now = datetime.utcnow()
    request_doc = {
        "userId": current_user.id,
        "type": request_data.type,
        "startDate": request_data.startDate,
        "endDate": request_data.endDate,
        "totalDays": business_days,
        "reason": request_data.reason,
        "comment": request_data.comment,
        "documents": request_data.documents,
        "status": "pending",
        "approvedBy": None,
        "approvedAt": None,
        "rejectionReason": None,
        "createdAt": now,
        "updatedAt": now
    }
    
    # Insert request
    result = await leave_requests_collection.insert_one(request_doc)
    
    # Get created request
    created_request = await leave_requests_collection.find_one({"_id": result.inserted_id})
    
    return LeaveRequest(
        id=str(created_request["_id"]),
        userId=str(created_request["userId"]),
        type=created_request["type"],
        startDate=created_request["startDate"],
        endDate=created_request["endDate"],
        totalDays=created_request["totalDays"],
        reason=created_request["reason"],
        comment=created_request.get("comment"),
        documents=created_request.get("documents", []),
        status=created_request["status"],
        approvedBy=None,
        approvedAt=None,
        rejectionReason=None,
        createdAt=created_request["createdAt"],
        updatedAt=created_request["updatedAt"]
    )


@router.put("/{request_id}", response_model=LeaveRequest)
async def update_leave_request(
    request_id: str,
    request_update: LeaveRequestUpdate,
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Update a leave request (only if pending)"""
    leave_requests_collection = get_leave_requests_collection()
    
    # Validate request_id
    try:
        object_id = ObjectId(request_id)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid request ID"
        )
    
    # Find the request
    existing_request = await leave_requests_collection.find_one({
        "_id": object_id,
        "userId": current_user.id
    })
    
    if not existing_request:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Leave request not found"
        )
    
    # Only allow updates to pending requests
    if existing_request["status"] != "pending":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only update pending requests"
        )
    
    # Prepare update data
    update_data = {k: v for k, v in request_update.dict().items() if v is not None}
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No data provided for update"
        )
    
    # Recalculate business days if dates are updated
    if "startDate" in update_data or "endDate" in update_data:
        start_date = update_data.get("startDate", existing_request["startDate"])
        end_date = update_data.get("endDate", existing_request["endDate"])
        
        if start_date >= end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="End date must be after start date"
            )
        
        update_data["totalDays"] = calculate_business_days(start_date, end_date)
    
    # Add updated timestamp
    update_data["updatedAt"] = datetime.utcnow()
    
    # Update request
    await leave_requests_collection.update_one(
        {"_id": object_id},
        {"$set": update_data}
    )
    
    # Get updated request
    updated_request = await leave_requests_collection.find_one({"_id": object_id})
    
    return LeaveRequest(
        id=str(updated_request["_id"]),
        userId=str(updated_request["userId"]),
        type=updated_request["type"],
        startDate=updated_request["startDate"],
        endDate=updated_request["endDate"],
        totalDays=updated_request["totalDays"],
        reason=updated_request["reason"],
        comment=updated_request.get("comment"),
        documents=updated_request.get("documents", []),
        status=updated_request["status"],
        approvedBy=str(updated_request["approvedBy"]) if updated_request.get("approvedBy") else None,
        approvedAt=updated_request.get("approvedAt"),
        rejectionReason=updated_request.get("rejectionReason"),
        createdAt=updated_request["createdAt"],
        updatedAt=updated_request["updatedAt"]
    )


@router.delete("/{request_id}")
async def cancel_leave_request(
    request_id: str,
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Cancel a leave request (only if pending)"""
    leave_requests_collection = get_leave_requests_collection()

    # Validate request_id
    try:
        object_id = ObjectId(request_id)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid request ID"
        )

    # Find and delete the request
    result = await leave_requests_collection.delete_one({
        "_id": object_id,
        "userId": current_user.id,
        "status": "pending"
    })

    if result.deleted_count == 0:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pending leave request not found"
        )

    return {"message": "Leave request cancelled successfully"}


@router.get("/balance", response_model=LeaveBalance)
async def get_leave_balance(
    year: Optional[int] = Query(None, ge=2020),
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Get leave balance for current user"""
    leave_requests_collection = get_leave_requests_collection()

    # Default to current year
    target_year = year or datetime.utcnow().year

    # Calculate date range for the year
    start_date = datetime(target_year, 1, 1)
    end_date = datetime(target_year, 12, 31, 23, 59, 59)

    # Get approved leave requests for the year
    cursor = leave_requests_collection.find({
        "userId": current_user.id,
        "status": "approved",
        "startDate": {"$gte": start_date, "$lte": end_date}
    })

    # Calculate used days by type
    used_vacation_days = 0
    used_sick_days = 0
    used_personal_days = 0

    async for request in cursor:
        if request["type"] == "vacation":
            used_vacation_days += request["totalDays"]
        elif request["type"] == "sick":
            used_sick_days += request["totalDays"]
        elif request["type"] == "personal":
            used_personal_days += request["totalDays"]

    # Standard allocations (could be made configurable per user)
    total_vacation_days = 25
    total_sick_days = 10
    total_personal_days = 5

    return LeaveBalance(
        userId=str(current_user.id),
        year=target_year,
        totalVacationDays=total_vacation_days,
        usedVacationDays=used_vacation_days,
        remainingVacationDays=max(0, total_vacation_days - used_vacation_days),
        totalSickDays=total_sick_days,
        usedSickDays=used_sick_days,
        remainingSickDays=max(0, total_sick_days - used_sick_days),
        totalPersonalDays=total_personal_days,
        usedPersonalDays=used_personal_days,
        remainingPersonalDays=max(0, total_personal_days - used_personal_days)
    )


# Manager/Admin endpoints
@router.get("/pending", response_model=PaginatedResponse[LeaveRequestWithUser])
async def get_pending_leave_requests(
    page: int = Query(1, ge=1),
    current_user: UserInDB = Depends(require_manager_or_admin)
):
    """Get all pending leave requests with pagination (manager/admin only)"""
    try:
        print(f"[API] Getting pending leave requests for manager {current_user.id}, page: {page}")

        leave_requests_collection = get_leave_requests_collection()
        users_collection = get_users_collection()

        # Pagination settings
        limit = 10
        skip = (page - 1) * limit

        # Get total count of pending requests
        total = await leave_requests_collection.count_documents({"status": "pending"})

        # Get pending requests with pagination
        cursor = leave_requests_collection.find({"status": "pending"}).sort("createdAt", 1).skip(skip).limit(limit)

        requests_with_users = []
        async for request_data in cursor:
            # Get user info
            user_data = await users_collection.find_one({"_id": request_data["userId"]})

            if user_data:
                try:
                    leave_request = LeaveRequestWithUser(
                        id=str(request_data["_id"]),
                        userId=str(request_data["userId"]),
                        type=request_data["type"],
                        startDate=request_data["startDate"],
                        endDate=request_data["endDate"],
                        totalDays=request_data["totalDays"],
                        reason=request_data["reason"],
                        comment=request_data.get("comment"),
                        documents=request_data.get("documents", []),
                        status=request_data["status"],
                        approvedBy=None,
                        approvedAt=None,
                        rejectionReason=None,
                        createdAt=request_data["createdAt"],
                        updatedAt=request_data.get("updatedAt"),  # Handle null values
                        user={
                            "id": str(user_data["_id"]),
                            "firstName": user_data["firstName"],
                            "lastName": user_data["lastName"],
                            "email": user_data["email"],
                            "department": user_data.get("department", ""),
                            "position": user_data.get("position", "")
                        }
                    )
                    requests_with_users.append(leave_request)
                except Exception as e:
                    print(f"[API] Error processing pending leave request {request_data.get('_id')}: {e}")
                    print(f"[API] Request data: {request_data}")
                    print(f"[API] User data: {user_data}")
                    # Skip this record and continue with others
                    continue

        # Calculate pagination info
        total_pages = (total + limit - 1) // limit

        print(f"[API] Found {len(requests_with_users)} pending leave requests, total: {total}, page: {page}/{total_pages}")

        return PaginatedResponse(
            data=requests_with_users,
            pagination={
                "page": page,
                "limit": limit,
                "total": total,
                "totalPages": total_pages
            }
        )

    except PyMongoError as e:
        logger.error(f"Database error in get_pending_leave_requests: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while retrieving pending leave requests"
        )
    except Exception as e:
        logger.error(f"Unexpected error in get_pending_leave_requests: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while retrieving pending leave requests"
        )


@router.post("/{request_id}/approve", response_model=LeaveRequest)
async def approve_leave_request(
    request_id: str,
    approval_data: ApprovalAction,
    current_user: UserInDB = Depends(require_manager_or_admin)
):
    """Approve or reject a leave request (manager/admin only)"""
    leave_requests_collection = get_leave_requests_collection()

    # Validate request_id
    try:
        object_id = ObjectId(request_id)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid request ID"
        )

    # Find the request
    existing_request = await leave_requests_collection.find_one({
        "_id": object_id,
        "status": "pending"
    })

    if not existing_request:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pending leave request not found"
        )

    # Prepare update data
    now = datetime.utcnow()
    update_data = {
        "status": approval_data.action + "d",  # "approve" -> "approved", "reject" -> "rejected"
        "approvedBy": current_user.id,
        "approvedAt": now,
        "updatedAt": now
    }

    if approval_data.action == "reject" and approval_data.rejectionReason:
        update_data["rejectionReason"] = approval_data.rejectionReason

    # Update request
    await leave_requests_collection.update_one(
        {"_id": object_id},
        {"$set": update_data}
    )

    # Get updated request
    updated_request = await leave_requests_collection.find_one({"_id": object_id})

    return LeaveRequest(
        id=str(updated_request["_id"]),
        userId=str(updated_request["userId"]),
        type=updated_request["type"],
        startDate=updated_request["startDate"],
        endDate=updated_request["endDate"],
        totalDays=updated_request["totalDays"],
        reason=updated_request["reason"],
        comment=updated_request.get("comment"),
        documents=updated_request.get("documents", []),
        status=updated_request["status"],
        approvedBy=str(updated_request["approvedBy"]),
        approvedAt=updated_request["approvedAt"],
        rejectionReason=updated_request.get("rejectionReason"),
        createdAt=updated_request["createdAt"],
        updatedAt=updated_request["updatedAt"]
    )
