#!/usr/bin/env python3
"""
Complete setup script for the FastAPI attendance backend.
This script sets up the environment and seeds the database.
"""

import asyncio
import os
import sys
from pathlib import Path

def create_env_file():
    """Create .env file if it doesn't exist"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("📝 Creating .env file from .env.example...")
        env_file.write_text(env_example.read_text())
        print("✅ Created .env file")
        print("⚠️  Please review and update the .env file with your settings")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("⚠️  No .env.example file found, creating basic .env file...")
        env_content = """# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/attendance_app
MONGODB_DB_NAME=attendance_app

# JWT Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# CORS Configuration (for Expo app)
ALLOWED_ORIGINS=["http://localhost:8081", "exp://*************:8081", "*"]
"""
        env_file.write_text(env_content)
        print("✅ Created basic .env file")

async def main():
    """Main setup function"""
    print("🚀 FastAPI Attendance Backend Setup")
    print("=" * 40)
    
    # Create .env file
    create_env_file()
    
    # Import and run database seeding
    try:
        print("\n🌱 Setting up database...")
        from seed_database import seed_database
        await seed_database()
        
        print("\n" + "=" * 40)
        print("✅ Setup completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Review the .env file and update settings if needed")
        print("2. Make sure MongoDB is running")
        print("3. Start the FastAPI server:")
        print("   python main.py")
        print("4. Visit http://localhost:8000/docs to see the API documentation")
        print("5. Update your Expo app's .env file with:")
        print("   EXPO_PUBLIC_API_URL=http://localhost:8000/api")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure MongoDB is running")
        print("2. Check your .env file configuration")
        print("3. Ensure all dependencies are installed: pip install -r requirements.txt")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
