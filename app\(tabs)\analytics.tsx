import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { ScreenWrapper } from '@/components/ui/ScreenWrapper';
import { Card } from '@/components/ui/Card';
import { useAppSelector } from '@/store/store';
import { selectCurrentUser } from '@/store/slices/authSlice';
import { useGetManagerStatsQuery } from '@/store/api/apiSlice';
import { TrendingUp, Users, Calendar, BarChart3 } from 'lucide-react-native';

export default function AnalyticsScreen() {
  const user = useAppSelector(selectCurrentUser);

  // Route guard: Only admins can access this screen
  if (!user || user.role !== 'admin') {
    return (
      <ScreenWrapper>
        <View style={styles.unauthorizedContainer}>
          <Text style={styles.unauthorizedText}>Accès non autorisé</Text>
          <Text style={styles.unauthorizedSubtext}>
            Cette page est réservée aux administrateurs.
          </Text>
        </View>
      </ScreenWrapper>
    );
  }
  const { isLoading: isLoadingStats } = useGetManagerStatsQuery();

  if (isLoadingStats) {
    return (
      <ScreenWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3b82f6" />
          <Text style={styles.loadingText}>Chargement des analytics...</Text>
        </View>
      </ScreenWrapper>
    );
  }

  const mockAnalytics = {
    monthlyAttendance: {
      january: 95,
      february: 92,
      march: 88,
      april: 94,
    },
    departmentStats: [
      { name: 'IT', employees: 8, attendanceRate: 96 },
      { name: 'HR', employees: 4, attendanceRate: 94 },
      { name: 'Sales', employees: 12, attendanceRate: 89 },
      { name: 'Marketing', employees: 6, attendanceRate: 91 },
    ],
    leaveStats: {
      totalRequests: 45,
      approved: 38,
      pending: 4,
      rejected: 3,
    },
    topPerformers: [
      { name: 'Marie Dubois', attendanceRate: 98, department: 'Marketing' },
      { name: 'Pierre Martin', attendanceRate: 97, department: 'Development' },
      { name: 'Sophie Laurent', attendanceRate: 96, department: 'Sales' },
    ],
  };

  return (
    <ScreenWrapper>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Analytics</Text>
          <Text style={styles.subtitle}>
            Rapports et statistiques détaillés
          </Text>
        </View>

        {/* Monthly Attendance Trend */}
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <TrendingUp size={24} color="#3b82f6" />
            <Text style={styles.cardTitle}>Tendance Mensuelle</Text>
          </View>
          <View style={styles.trendContainer}>
            {Object.entries(mockAnalytics.monthlyAttendance).map(
              ([month, rate]) => (
                <View key={month} style={styles.trendItem}>
                  <Text style={styles.monthLabel}>
                    {month.charAt(0).toUpperCase() + month.slice(1, 3)}
                  </Text>
                  <View style={styles.barContainer}>
                    <View
                      style={[
                        styles.bar,
                        {
                          height: `${rate}%`,
                          backgroundColor: rate >= 90 ? '#10B981' : '#F59E0B',
                        },
                      ]}
                    />
                  </View>
                  <Text style={styles.rateText}>{rate}%</Text>
                </View>
              )
            )}
          </View>
        </Card>

        {/* Department Statistics */}
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <Users size={24} color="#8B5CF6" />
            <Text style={styles.cardTitle}>Statistiques par Département</Text>
          </View>
          {mockAnalytics.departmentStats.map((dept) => (
            <View key={dept.name} style={styles.deptItem}>
              <View style={styles.deptInfo}>
                <Text style={styles.deptName}>{dept.name}</Text>
                <Text style={styles.deptEmployees}>
                  {dept.employees} employés
                </Text>
              </View>
              <View style={styles.deptRate}>
                <Text
                  style={[
                    styles.rateValue,
                    {
                      color: dept.attendanceRate >= 90 ? '#10B981' : '#F59E0B',
                    },
                  ]}
                >
                  {dept.attendanceRate}%
                </Text>
              </View>
            </View>
          ))}
        </Card>

        {/* Leave Statistics */}
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <Calendar size={24} color="#F59E0B" />
            <Text style={styles.cardTitle}>Statistiques des Congés</Text>
          </View>
          <View style={styles.leaveStats}>
            <View style={styles.leaveStat}>
              <Text style={styles.leaveNumber}>
                {mockAnalytics.leaveStats.totalRequests}
              </Text>
              <Text style={styles.leaveLabel}>Total</Text>
            </View>
            <View style={styles.leaveStat}>
              <Text style={[styles.leaveNumber, { color: '#10B981' }]}>
                {mockAnalytics.leaveStats.approved}
              </Text>
              <Text style={styles.leaveLabel}>Approuvées</Text>
            </View>
            <View style={styles.leaveStat}>
              <Text style={[styles.leaveNumber, { color: '#F59E0B' }]}>
                {mockAnalytics.leaveStats.pending}
              </Text>
              <Text style={styles.leaveLabel}>En attente</Text>
            </View>
            <View style={styles.leaveStat}>
              <Text style={[styles.leaveNumber, { color: '#EF4444' }]}>
                {mockAnalytics.leaveStats.rejected}
              </Text>
              <Text style={styles.leaveLabel}>Refusées</Text>
            </View>
          </View>
        </Card>

        {/* Top Performers */}
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <BarChart3 size={24} color="#10B981" />
            <Text style={styles.cardTitle}>Meilleurs Performances</Text>
          </View>
          {mockAnalytics.topPerformers.map((performer, index) => (
            <View key={performer.name} style={styles.performerItem}>
              <View style={styles.performerRank}>
                <Text style={styles.rankNumber}>{index + 1}</Text>
              </View>
              <View style={styles.performerInfo}>
                <Text style={styles.performerName}>{performer.name}</Text>
                <Text style={styles.performerDept}>{performer.department}</Text>
              </View>
              <View style={styles.performerRate}>
                <Text style={styles.performerScore}>
                  {performer.attendanceRate}%
                </Text>
              </View>
            </View>
          ))}
        </Card>
      </ScrollView>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '500',
  },
  header: {
    padding: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
  },
  card: {
    margin: 16,
    padding: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginLeft: 12,
  },
  trendContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    height: 120,
  },
  trendItem: {
    alignItems: 'center',
    flex: 1,
  },
  monthLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 8,
  },
  barContainer: {
    height: 80,
    width: 20,
    backgroundColor: '#f3f4f6',
    borderRadius: 4,
    justifyContent: 'flex-end',
  },
  bar: {
    width: '100%',
    borderRadius: 4,
    minHeight: 4,
  },
  rateText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#111827',
    marginTop: 4,
  },
  deptItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  deptInfo: {
    flex: 1,
  },
  deptName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  deptEmployees: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  deptRate: {
    alignItems: 'flex-end',
  },
  rateValue: {
    fontSize: 18,
    fontWeight: '700',
  },
  leaveStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  leaveStat: {
    alignItems: 'center',
  },
  leaveNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: '#111827',
  },
  leaveLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  performerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  performerRank: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  rankNumber: {
    fontSize: 14,
    fontWeight: '700',
    color: '#ffffff',
  },
  performerInfo: {
    flex: 1,
  },
  performerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  performerDept: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  performerRate: {
    alignItems: 'flex-end',
  },
  performerScore: {
    fontSize: 18,
    fontWeight: '700',
    color: '#10B981',
  },
  unauthorizedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  unauthorizedText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ef4444',
    textAlign: 'center',
  },
  unauthorizedSubtext: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 8,
  },
});
