from pydantic import BaseModel
from typing import Generic, TypeVar, List, Optional

T = TypeVar('T')


class ApiResponse(BaseModel, Generic[T]):
    success: bool = True
    message: str = "Success"
    data: Optional[T] = None


class PaginatedResponse(BaseModel, Generic[T]):
    data: List[T]
    pagination: dict


class ErrorResponse(BaseModel):
    success: bool = False
    message: str
    error_code: Optional[str] = None
