from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from bson import ObjectId
from pymongo.errors import DuplicateKeyError

from ..models.user import UserCreate, UserUpdate, User, UserInDB, UserProfile
from ..models.common import PaginatedResponse
from ..auth import get_current_active_user, require_admin, get_password_hash
from ..database import get_users_collection

router = APIRouter()


@router.get("/profile", response_model=UserProfile)
async def get_user_profile(current_user: UserInDB = Depends(get_current_active_user)):
    """Get current user profile"""
    return UserProfile(
        id=str(current_user.id),
        email=current_user.email,
        firstName=current_user.firstName,
        lastName=current_user.lastName,
        role=current_user.role,
        department=current_user.department,
        position=current_user.position,
        phone=current_user.phone,
        profilePicture=current_user.profilePicture,
        hireDate=current_user.hireDate,
        workSchedule=current_user.workSchedule
    )


@router.put("/profile", response_model=UserProfile)
async def update_user_profile(
    user_update: UserUpdate,
    current_user: UserInDB = Depends(get_current_active_user)
):
    """Update current user profile"""
    users_collection = get_users_collection()
    
    # Prepare update data (exclude None values)
    update_data = {k: v for k, v in user_update.dict().items() if v is not None}
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No data provided for update"
        )
    
    # Add updated timestamp
    update_data["updatedAt"] = datetime.utcnow()
    
    # Check if email is being updated and if it's unique
    if "email" in update_data and update_data["email"] != current_user.email:
        existing_user = await users_collection.find_one({"email": update_data["email"]})
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
    
    try:
        # Update user in database
        result = await users_collection.update_one(
            {"_id": current_user.id},
            {"$set": update_data}
        )
        
        if result.modified_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Get updated user
        updated_user_data = await users_collection.find_one({"_id": current_user.id})
        updated_user = UserInDB(**updated_user_data)
        
        return UserProfile(
            id=str(updated_user.id),
            email=updated_user.email,
            firstName=updated_user.firstName,
            lastName=updated_user.lastName,
            role=updated_user.role,
            department=updated_user.department,
            position=updated_user.position,
            phone=updated_user.phone,
            profilePicture=updated_user.profilePicture,
            hireDate=updated_user.hireDate,
            workSchedule=updated_user.workSchedule
        )
        
    except DuplicateKeyError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )


@router.get("/", response_model=PaginatedResponse[User])
async def get_all_users(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    search: Optional[str] = Query(None),
    department: Optional[str] = Query(None),
    role: Optional[str] = Query(None),
    current_user: UserInDB = Depends(require_admin)
):
    """Get all users (admin only)"""
    try:
        users_collection = get_users_collection()
    
    # Build query
    query = {"isActive": True}
    
    if search:
        query["$or"] = [
            {"firstName": {"$regex": search, "$options": "i"}},
            {"lastName": {"$regex": search, "$options": "i"}},
            {"email": {"$regex": search, "$options": "i"}}
        ]
    
    if department:
        query["department"] = department
    
    if role:
        query["role"] = role
    
    # Get total count
    total = await users_collection.count_documents(query)
    
    # Get paginated results
    skip = (page - 1) * size
    cursor = users_collection.find(query).skip(skip).limit(size).sort("createdAt", -1)
    
    users = []
    async for user_data in cursor:
        try:
            # Safely extract user data with defaults for missing fields
            user = User(
                id=str(user_data["_id"]),
                email=user_data.get("email", ""),
                firstName=user_data.get("firstName", ""),
                lastName=user_data.get("lastName", ""),
                role=user_data.get("role", "employee"),
                department=user_data.get("department", ""),
                position=user_data.get("position", ""),
                phone=user_data.get("phone"),
                isActive=user_data.get("isActive", True),
                profilePicture=user_data.get("profilePicture"),
                workSchedule=user_data.get("workSchedule"),
                hireDate=user_data.get("hireDate", datetime.utcnow()),
                createdAt=user_data.get("createdAt", datetime.utcnow()),
                updatedAt=user_data.get("updatedAt", datetime.utcnow())
            )
            users.append(user)
        except Exception as e:
            # Log the error but continue processing other users
            print(f"Error processing user {user_data.get('_id', 'unknown')}: {e}")
            continue
    
        pages = (total + size - 1) // size

        return PaginatedResponse(
            items=users,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
    except Exception as e:
        print(f"Error in get_all_users: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching users"
        )


@router.post("/", response_model=User)
async def create_user(
    user_data: UserCreate,
    current_user: UserInDB = Depends(require_admin)
):
    """Create new user (admin only)"""
    users_collection = get_users_collection()

    # Check if email already exists
    existing_user = await users_collection.find_one({"email": user_data.email})
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Hash password
    hashed_password = get_password_hash(user_data.password)

    # Create user document
    now = datetime.utcnow()
    user_doc = {
        "email": user_data.email,
        "password": hashed_password,
        "firstName": user_data.firstName,
        "lastName": user_data.lastName,
        "role": user_data.role,
        "department": user_data.department,
        "position": user_data.position,
        "phone": user_data.phone,
        "isActive": user_data.isActive,
        "profilePicture": user_data.profilePicture,
        "workSchedule": user_data.workSchedule.dict() if user_data.workSchedule else None,
        "hireDate": now,
        "createdAt": now,
        "updatedAt": now
    }

    try:
        # Insert user
        result = await users_collection.insert_one(user_doc)

        # Get created user
        created_user_data = await users_collection.find_one({"_id": result.inserted_id})

        return User(
            id=str(created_user_data["_id"]),
            email=created_user_data.get("email", ""),
            firstName=created_user_data.get("firstName", ""),
            lastName=created_user_data.get("lastName", ""),
            role=created_user_data.get("role", "employee"),
            department=created_user_data.get("department", ""),
            position=created_user_data.get("position", ""),
            phone=created_user_data.get("phone"),
            isActive=created_user_data.get("isActive", True),
            profilePicture=created_user_data.get("profilePicture"),
            workSchedule=created_user_data.get("workSchedule"),
            hireDate=created_user_data.get("hireDate", datetime.utcnow()),
            createdAt=created_user_data.get("createdAt", datetime.utcnow()),
            updatedAt=created_user_data.get("updatedAt", datetime.utcnow())
        )

    except DuplicateKeyError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )


@router.put("/{user_id}", response_model=User)
async def update_user(
    user_id: str,
    user_update: UserUpdate,
    current_user: UserInDB = Depends(require_admin)
):
    """Update user (admin only)"""
    users_collection = get_users_collection()

    # Validate user_id
    try:
        object_id = ObjectId(user_id)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user ID"
        )

    # Check if user exists
    existing_user = await users_collection.find_one({"_id": object_id})
    if not existing_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Prepare update data
    update_data = {k: v for k, v in user_update.dict().items() if v is not None}

    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No data provided for update"
        )

    # Add updated timestamp
    update_data["updatedAt"] = datetime.utcnow()

    # Check email uniqueness if being updated
    if "email" in update_data and update_data["email"] != existing_user["email"]:
        email_exists = await users_collection.find_one({"email": update_data["email"]})
        if email_exists:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

    try:
        # Update user
        result = await users_collection.update_one(
            {"_id": object_id},
            {"$set": update_data}
        )

        if result.modified_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        # Get updated user
        updated_user_data = await users_collection.find_one({"_id": object_id})

        return User(
            id=str(updated_user_data["_id"]),
            email=updated_user_data.get("email", ""),
            firstName=updated_user_data.get("firstName", ""),
            lastName=updated_user_data.get("lastName", ""),
            role=updated_user_data.get("role", "employee"),
            department=updated_user_data.get("department", ""),
            position=updated_user_data.get("position", ""),
            phone=updated_user_data.get("phone"),
            isActive=updated_user_data.get("isActive", True),
            profilePicture=updated_user_data.get("profilePicture"),
            workSchedule=updated_user_data.get("workSchedule"),
            hireDate=updated_user_data.get("hireDate", datetime.utcnow()),
            createdAt=updated_user_data.get("createdAt", datetime.utcnow()),
            updatedAt=updated_user_data.get("updatedAt", datetime.utcnow())
        )

    except DuplicateKeyError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )


@router.delete("/{user_id}")
async def delete_user(
    user_id: str,
    current_user: UserInDB = Depends(require_admin)
):
    """Delete user (soft delete - set isActive to False)"""
    users_collection = get_users_collection()

    # Validate user_id
    try:
        object_id = ObjectId(user_id)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid user ID"
        )

    # Prevent self-deletion
    if str(current_user.id) == user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own account"
        )

    # Soft delete user
    result = await users_collection.update_one(
        {"_id": object_id},
        {"$set": {"isActive": False, "updatedAt": datetime.utcnow()}}
    )

    if result.modified_count == 0:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return {"message": "User deleted successfully"}
