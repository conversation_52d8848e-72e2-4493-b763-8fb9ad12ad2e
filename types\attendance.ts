export interface AttendanceRecord {
  id: string;
  userId: string;
  date: string;
  clockIn?: string;
  clockOut?: string;
  totalTime?: string;
  status: 'present' | 'absent' | 'late' | 'half_day';
  notes?: string;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
}

export interface ClockActionPayload {
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  notes?: string;
}

export interface AttendanceStatus {
  isClockedIn: boolean;
  clockInTime?: string;
  clockOutTime?: string;
  totalTime: number;
  status: string;
}

export interface AttendanceHistory {
  records: AttendanceRecord[];
  stats: {
    totalDays: number;
    presentDays: number;
    absentDays: number;
    lateDays: number;
    totalHours: number;
    averageHours: number;
    overtimeHours: number;
  };
  month: number;
  year: number;
}

export interface AttendanceStats {
  today: AttendanceRecord | null;
  thisWeek: {
    totalHours: string;
    daysPresent: number;
    daysAbsent: number;
  };
  thisMonth: {
    totalHours: string;
    daysPresent: number;
    daysAbsent: number;
    attendanceRate: number;
  };
}
