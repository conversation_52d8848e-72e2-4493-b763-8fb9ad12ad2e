import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Button } from './Button';
import { Card } from './Card';
import { useAppDispatch, useAppSelector } from '@/store/store';
import {
  startTimer,
  pauseTimer,
  selectIsTimerRunning,
  selectTimerState,
} from '@/store/slices/timerSlice';
import { Play, Pause } from 'lucide-react-native';

interface TimerProps {
  onSaveToHistory?: (totalTime: number) => void;
  onStart?: () => void;
  onPause?: () => void;
  isRunning?: boolean;
  currentElapsedTime?: number;
}

export const Timer: React.FC<TimerProps> = ({
  onSaveToHistory,
  onStart,
  onPause,
  isRunning: propIsRunning,
  currentElapsedTime: propElapsedTime,
}) => {
  const dispatch = useAppDispatch();
  const storeIsRunning = useAppSelector(selectIsTimerRunning);
  const timerState = useAppSelector(selectTimerState);

  // Use props if provided, otherwise fall back to store values
  const isRunning =
    propIsRunning !== undefined ? propIsRunning : storeIsRunning;
  const baseElapsedTime =
    propElapsedTime !== undefined ? propElapsedTime : timerState.elapsedTime;

  // Local state to hold the current display time
  const [displayTime, setDisplayTime] = useState(0);

  // Update display time based on timer state
  useEffect(() => {
    const updateDisplayTime = () => {
      if (isRunning && timerState.startTime !== null) {
        const currentTime = Date.now();
        const sessionDuration = Math.floor(
          (currentTime - timerState.startTime) / 1000
        );
        const totalTime = baseElapsedTime + sessionDuration;
        setDisplayTime(totalTime);
      } else {
        setDisplayTime(baseElapsedTime);
      }
    };

    // Update immediately
    updateDisplayTime();

    // Set up interval if timer is running
    let intervalId: number | null = null;
    if (isRunning) {
      intervalId = setInterval(updateDisplayTime, 1000);
    }

    // Cleanup function
    return () => {
      if (intervalId !== null) {
        clearInterval(intervalId);
      }
    };
  }, [isRunning, timerState.startTime, baseElapsedTime]);

  // Format time from seconds to HH:MM:SS
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleToggle = () => {
    if (isRunning) {
      // Use prop function if provided, otherwise dispatch to store
      if (onPause) {
        onPause();
      } else {
        dispatch(pauseTimer());
      }
    } else {
      // Use prop function if provided, otherwise dispatch to store
      if (onStart) {
        onStart();
      } else {
        dispatch(startTimer());
      }
    }
  };

  return (
    <Card style={styles.container}>
      <View style={styles.timerDisplay}>
        <Text style={styles.timerText}>{formatTime(displayTime)}</Text>
        <Text style={styles.timerLabel}>Temps de travail</Text>
      </View>

      <View style={styles.buttonContainer}>
        <Button
          title={isRunning ? 'Arrêter' : 'Démarrer'}
          variant={isRunning ? 'danger' : 'primary'}
          size="large"
          onPress={handleToggle}
          style={styles.toggleButton}
          icon={
            isRunning ? (
              <Pause size={20} color="#ffffff" />
            ) : (
              <Play size={20} color="#ffffff" />
            )
          }
        />
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    alignItems: 'center',
  },
  timerDisplay: {
    alignItems: 'center',
    marginBottom: 32,
  },
  timerText: {
    fontSize: 56,
    fontWeight: '700',
    color: '#111827',
    fontFamily: 'monospace',
    letterSpacing: 2,
  },
  timerLabel: {
    fontSize: 18,
    color: '#6b7280',
    marginTop: 12,
    fontWeight: '500',
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
  },
  toggleButton: {
    minWidth: 200,
    paddingHorizontal: 32,
  },
});
