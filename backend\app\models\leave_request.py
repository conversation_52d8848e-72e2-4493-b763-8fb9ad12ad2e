from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from bson import ObjectId
from .user import PyObjectId


class LeaveRequestBase(BaseModel):
    type: str = Field(..., pattern="^(vacation|sick|personal|maternity|paternity|other)$")
    startDate: datetime
    endDate: datetime
    totalDays: int = Field(..., gt=0)
    reason: str = Field(..., min_length=1, max_length=500)
    comment: Optional[str] = Field(None, max_length=1000)
    documents: List[str] = Field(default_factory=list, description="URLs to supporting documents")


class LeaveRequestCreate(LeaveRequestBase):
    pass


class LeaveRequestUpdate(BaseModel):
    type: Optional[str] = Field(None, pattern="^(vacation|sick|personal|maternity|paternity|other)$")
    startDate: Optional[datetime] = None
    endDate: Optional[datetime] = None
    totalDays: Optional[int] = Field(None, gt=0)
    reason: Optional[str] = Field(None, min_length=1, max_length=500)
    comment: Optional[str] = Field(None, max_length=1000)
    documents: Optional[List[str]] = None


class LeaveRequestInDB(LeaveRequestBase):
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    userId: PyObjectId
    status: str = Field(default="pending", pattern="^(pending|approved|rejected)$")
    approvedBy: Optional[PyObjectId] = None
    approvedAt: Optional[datetime] = None
    rejectionReason: Optional[str] = None
    createdAt: datetime
    updatedAt: datetime

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class LeaveRequest(LeaveRequestBase):
    id: str = Field(alias="_id")
    userId: str
    status: str
    approvedBy: Optional[str] = None
    approvedAt: Optional[datetime] = None
    rejectionReason: Optional[str] = None
    createdAt: datetime
    updatedAt: Optional[datetime] = None

    class Config:
        populate_by_name = True
        json_encoders = {ObjectId: str}


class LeaveRequestWithUser(LeaveRequest):
    user: dict = Field(..., description="User information")


class ApprovalAction(BaseModel):
    action: str = Field(..., pattern="^(approve|reject)$")
    rejectionReason: Optional[str] = Field(None, max_length=500)


class LeaveBalance(BaseModel):
    userId: str
    year: int
    totalVacationDays: int = Field(default=25)
    usedVacationDays: int = Field(default=0)
    remainingVacationDays: int = Field(default=25)
    totalSickDays: int = Field(default=10)
    usedSickDays: int = Field(default=0)
    remainingSickDays: int = Field(default=10)
    totalPersonalDays: int = Field(default=5)
    usedPersonalDays: int = Field(default=0)
    remainingPersonalDays: int = Field(default=5)
