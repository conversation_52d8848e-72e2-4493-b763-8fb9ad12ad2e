import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { useState } from 'react';
import { ScreenWrapper } from '@/components/ui/ScreenWrapper';
import { Card } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { useGetAttendanceHistoryQuery } from '@/store/api/apiSlice';
import { Calendar, Clock, MapPin } from 'lucide-react-native';

export default function HistoryScreen() {
  const [selectedMonth, _setSelectedMonth] = useState(new Date().getMonth());
  const [selectedYear] = useState(new Date().getFullYear());

  const {
    data: attendanceHistory,
    isLoading: loading,
    error,
  } = useGetAttendanceHistoryQuery({
    month: selectedMonth + 1, // API expects 1-based month
    year: selectedYear,
  });

  // Debug logging
  console.log('[History] API Response:', {
    data: attendanceHistory,
    loading,
    error,
    month: selectedMonth + 1,
    year: selectedYear,
  });

  const history = attendanceHistory?.records || [];
  const attendanceStats = attendanceHistory?.stats;

  const renderHistoryItem = ({ item }: { item: any }) => {
    // Format date properly
    const formatDate = (dateStr: string) => {
      try {
        const date = new Date(dateStr);
        return date.toLocaleDateString('fr-FR', {
          weekday: 'short',
          day: '2-digit',
          month: 'short',
        });
      } catch {
        return dateStr;
      }
    };

    // Format time properly
    const formatTime = (timeStr: string | null) => {
      if (!timeStr) return null;
      try {
        const date = new Date(timeStr);
        return date.toLocaleTimeString('fr-FR', {
          hour: '2-digit',
          minute: '2-digit',
        });
      } catch {
        return timeStr;
      }
    };

    // Format total time from minutes
    const formatTotalTime = (minutes: number) => {
      if (!minutes) return 'En cours';
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${hours}h ${mins}min`;
    };

    return (
      <Card style={styles.historyCard}>
        <View style={styles.historyHeader}>
          <View style={styles.dateContainer}>
            <Calendar size={16} color="#6b7280" />
            <Text style={styles.dateText}>{formatDate(item.date)}</Text>
          </View>
          <Badge
            variant={
              item.status === 'present' || item.status === 'late'
                ? 'success'
                : 'danger'
            }
            text={
              item.status === 'present'
                ? 'Présent'
                : item.status === 'late'
                ? 'Retard'
                : 'Absent'
            }
          />
        </View>

        {(item.status === 'present' || item.status === 'late') && (
          <View style={styles.timeContainer}>
            {item.clockIn && (
              <View style={styles.timeRow}>
                <Clock size={16} color="#10B981" />
                <Text style={styles.timeText}>
                  Arrivée: {formatTime(item.clockIn)}
                </Text>
              </View>
            )}
            {item.clockOut && (
              <View style={styles.timeRow}>
                <Clock size={16} color="#EF4444" />
                <Text style={styles.timeText}>
                  Départ: {formatTime(item.clockOut)}
                </Text>
              </View>
            )}
            <View style={styles.timeRow}>
              <MapPin size={16} color="#6b7280" />
              <Text style={styles.timeText}>
                Temps total: {formatTotalTime(item.totalTime)}
              </Text>
            </View>
          </View>
        )}

        {item.notes && (
          <View style={styles.notesContainer}>
            <Text style={styles.notesText}>{item.notes}</Text>
          </View>
        )}
      </Card>
    );
  };

  if (loading) {
    return (
      <ScreenWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3b82f6" />
          <Text style={styles.loadingText}>Chargement de l'historique...</Text>
        </View>
      </ScreenWrapper>
    );
  }

  if (error) {
    return (
      <ScreenWrapper>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Erreur lors du chargement de l'historique
          </Text>
        </View>
      </ScreenWrapper>
    );
  }

  const stats = {
    presentDays: Math.max(0, attendanceStats?.presentDays || 0),
    totalHours: Math.max(0, attendanceStats?.totalHours || 0),
    averageHours: Math.max(0, attendanceStats?.averageHours || 0),
  };

  return (
    <ScreenWrapper>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Historique</Text>
          <Text style={styles.subtitle}>
            {new Date(selectedYear, selectedMonth).toLocaleDateString('fr-FR', {
              month: 'long',
              year: 'numeric',
            })}
          </Text>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{stats.presentDays}</Text>
            <Text style={styles.statLabel}>Jours travaillés</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {Math.max(0, Math.round(stats.totalHours))}h
            </Text>
            <Text style={styles.statLabel}>Heures totales</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {Math.max(0, stats.averageHours).toFixed(1)}h
            </Text>
            <Text style={styles.statLabel}>Moyenne/jour</Text>
          </View>
        </View>

        <FlatList
          data={history}
          renderItem={renderHistoryItem}
          keyExtractor={(item, index) => item?.id || `history-item-${index}`}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshing={loading}
          onRefresh={() => {}}
        />
      </View>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#111827',
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#111827',
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  listContainer: {
    padding: 16,
  },
  historyCard: {
    marginBottom: 12,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  timeContainer: {
    gap: 8,
  },
  timeRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#374151',
  },
  notesContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  notesText: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
    textAlign: 'center',
  },
});
