import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { RootState } from '../store';
import {
  User,
  LoginCredentials,
  LoginResponse,
  AttendanceRecord,
  AttendanceStatus,
  ClockActionPayload,
  AttendanceHistory,
  AttendanceStats,
  LeaveRequest,
  CreateLeaveRequest,
  LeaveBalance,
  ApprovalAction,
  ApiResponse,
  PaginatedResponse,
} from '@/types';

// Define the API base URL - FastAPI backend runs on port 8000
const API_BASE_URL = 'http://10.1.0.168:8000/api';

// Debug logging
console.log('[API] Using API_BASE_URL:', API_BASE_URL);

// Enhanced base query with comprehensive logging
const baseQueryWithLogging = fetchBaseQuery({
  baseUrl: API_BASE_URL,
  prepareHeaders: (headers, { getState }) => {
    // Add authentication token to requests
    const token = (getState() as RootState).auth.token;
    const authState = (getState() as RootState).auth;

    console.log('[API] PrepareHeaders - Auth state:', {
      isAuthenticated: authState.isAuthenticated,
      hasToken: !!token,
      tokenLength: token?.length || 0,
      tokenPreview: token ? `${token.substring(0, 20)}...` : 'null',
    });

    if (token) {
      headers.set('authorization', `Bearer ${token}`);
      console.log('[API] Authorization header set with token');
    } else {
      console.log('[API] No token available - request will be unauthenticated');
    }
    headers.set('content-type', 'application/json');
    return headers;
  },
});

// Logging middleware wrapper
const baseQueryWithInterceptor = async (
  args: any,
  api: any,
  extraOptions: any
) => {
  const startTime = Date.now();

  // Extract request details
  const url = typeof args === 'string' ? args : args.url;
  const method = typeof args === 'string' ? 'GET' : args.method || 'GET';
  const body = typeof args === 'object' ? args.body : undefined;

  // Log request
  console.log(`🚀 [API] ${method} ${API_BASE_URL}${url}`);
  if (body && method !== 'GET') {
    console.log(`📤 [API] Request payload:`, body);
  }

  // Make the actual request
  const result = await baseQueryWithLogging(args, api, extraOptions);

  const duration = Date.now() - startTime;

  // Log response
  if (result.error) {
    console.error(
      `❌ [API] ${method} ${url} - ${result.error.status} (${duration}ms)`
    );
    console.error(`📥 [API] Error details:`, result.error);

    // Handle 401 errors - token might be expired or invalid
    if (result.error.status === 401) {
      console.warn('[API] 401 Unauthorized - Token may be expired or invalid');
      // Import AuthService dynamically to avoid circular dependency
      import('@/services/authService').then(({ AuthService }) => {
        console.log('[API] Logging out user due to 401 error');
        AuthService.logout();
      });
    }
  } else {
    console.log(`✅ [API] ${method} ${url} - 200 (${duration}ms)`);
    if (result.data) {
      // Log a summary of the response data
      const dataSummary = Array.isArray(result.data)
        ? `Array[${result.data.length}]`
        : typeof result.data === 'object'
        ? `Object{${Object.keys(result.data).join(', ')}}`
        : result.data;
      console.log(`📥 [API] Response summary:`, dataSummary);
    }
  }

  return result;
};

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithInterceptor,
  tagTypes: ['User', 'Attendance', 'Leave', 'Team', 'Notification'],
  endpoints: (builder) => ({
    // Authentication endpoints
    login: builder.mutation<LoginResponse, LoginCredentials>({
      query: (credentials) => ({
        url: '/auth/login',
        method: 'POST',
        body: credentials,
      }),
    }),

    logout: builder.mutation<void, void>({
      query: () => ({
        url: '/auth/logout',
        method: 'POST',
      }),
      invalidatesTags: ['User', 'Attendance', 'Leave'],
    }),

    // User profile endpoints
    getProfile: builder.query<User, void>({
      query: () => '/user/profile',
      providesTags: ['User'],
    }),

    updateProfile: builder.mutation<User, Partial<User>>({
      query: (updates) => ({
        url: '/user/profile',
        method: 'PUT',
        body: updates,
      }),
      invalidatesTags: ['User'],
    }),

    // Attendance endpoints
    getAttendanceStatus: builder.query<AttendanceStatus, void>({
      query: () => '/attendance/status',
      providesTags: ['Attendance'],
    }),

    clockIn: builder.mutation<AttendanceRecord, ClockActionPayload>({
      query: (payload) => ({
        url: '/attendance/clock-in',
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: ['Attendance'],
    }),

    clockOut: builder.mutation<AttendanceRecord, ClockActionPayload>({
      query: (payload) => ({
        url: '/attendance/clock-out',
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: ['Attendance'],
    }),

    getAttendanceHistory: builder.query<
      AttendanceHistory,
      { month?: number; year?: number }
    >({
      query: ({ month, year } = {}) => {
        const params = new URLSearchParams();
        if (month) params.append('month', month.toString());
        if (year) params.append('year', year.toString());
        return `/attendance/history?${params.toString()}`;
      },
      providesTags: ['Attendance'],
    }),

    getAttendanceStats: builder.query<AttendanceStats, void>({
      query: () => '/attendance/stats',
      providesTags: ['Attendance'],
    }),

    // Leave request endpoints
    getLeaveRequests: builder.query<
      PaginatedResponse<LeaveRequest>,
      { page?: number; status?: string }
    >({
      query: ({ page = 1, status } = {}) => {
        const params = new URLSearchParams();
        params.append('page', page.toString());
        if (status) params.append('status', status);
        return `/leave?${params.toString()}`;
      },
      providesTags: ['Leave'],
    }),

    createLeaveRequest: builder.mutation<LeaveRequest, CreateLeaveRequest>({
      query: (request) => ({
        url: '/leave',
        method: 'POST',
        body: request,
      }),
      invalidatesTags: ['Leave'],
    }),

    updateLeaveRequest: builder.mutation<
      LeaveRequest,
      { id: string; updates: Partial<CreateLeaveRequest> }
    >({
      query: ({ id, updates }) => ({
        url: `/leave/requests/${id}`,
        method: 'PUT',
        body: updates,
      }),
      invalidatesTags: ['Leave'],
    }),

    cancelLeaveRequest: builder.mutation<void, string>({
      query: (id) => ({
        url: `/leave/requests/${id}/cancel`,
        method: 'POST',
      }),
      invalidatesTags: ['Leave'],
    }),

    getLeaveBalance: builder.query<LeaveBalance, void>({
      query: () => '/leave/balance',
      providesTags: ['Leave'],
    }),

    // Manager/Admin endpoints for leave approvals
    getPendingLeaveRequests: builder.query<
      PaginatedResponse<LeaveRequest>,
      { page?: number }
    >({
      query: ({ page = 1 } = {}) => `/leave/pending?page=${page}`,
      providesTags: ['Leave'],
    }),

    approveLeaveRequest: builder.mutation<LeaveRequest, ApprovalAction>({
      query: ({ requestId, action, comment }) => ({
        url: `/leave/requests/${requestId}/approve`,
        method: 'POST',
        body: { action, comment },
      }),
      invalidatesTags: ['Leave'],
    }),

    // Manager Dashboard endpoints
    getManagerStats: builder.query<
      {
        todayStats: {
          totalEmployees: number;
          onLeave: number;
          absent: number;
          present: number;
        };
        weeklyStats: {
          attendanceRate: number;
          avgHoursWorked: string;
        };
        pendingRequests: number;
      },
      void
    >({
      query: () => '/manager/stats',
      providesTags: ['Team'],
    }),

    getTeamAbsences: builder.query<
      {
        onLeave: Array<{
          id: string;
          name: string;
          type: string;
          endDate: string;
        }>;
        absent: Array<{ id: string; name: string; reason: string }>;
      },
      void
    >({
      query: () => '/manager/team-absences',
      providesTags: ['Team'],
    }),

    // Admin User Management endpoints
    getAllUsers: builder.query<
      PaginatedResponse<User>,
      { page?: number; search?: string }
    >({
      query: ({ page = 1, search = '' } = {}) => {
        const params = new URLSearchParams();
        params.append('page', page.toString());
        if (search) params.append('search', search);
        return `/users?${params.toString()}`;
      },
      providesTags: ['User'],
    }),

    createUser: builder.mutation<User, Omit<User, 'id' | 'hireDate'>>({
      query: (userData) => ({
        url: '/users',
        method: 'POST',
        body: userData,
      }),
      invalidatesTags: ['User'],
    }),

    updateUser: builder.mutation<User, { id: string; updates: Partial<User> }>({
      query: ({ id, updates }) => ({
        url: `/users/${id}`,
        method: 'PUT',
        body: updates,
      }),
      invalidatesTags: ['User'],
    }),

    deleteUser: builder.mutation<void, string>({
      query: (id) => ({
        url: `/users/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['User'],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useLoginMutation,
  useLogoutMutation,
  useGetProfileQuery,
  useUpdateProfileMutation,
  useGetAttendanceStatusQuery,
  useClockInMutation,
  useClockOutMutation,
  useGetAttendanceHistoryQuery,
  useGetAttendanceStatsQuery,
  useGetLeaveRequestsQuery,
  useCreateLeaveRequestMutation,
  useUpdateLeaveRequestMutation,
  useCancelLeaveRequestMutation,
  useGetLeaveBalanceQuery,
  useGetPendingLeaveRequestsQuery,
  useApproveLeaveRequestMutation,
  useGetManagerStatsQuery,
  useGetTeamAbsencesQuery,
  useGetAllUsersQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
} = apiSlice;
