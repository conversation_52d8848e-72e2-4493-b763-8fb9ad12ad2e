from pydantic import BaseModel, EmailStr, Field
from typing import Optional, Dict
from datetime import datetime
from bson import ObjectId


class PyObjectId(ObjectId):
    @classmethod
    def __get_pydantic_core_schema__(cls, source_type, handler):
        from pydantic_core import core_schema
        return core_schema.no_info_plain_validator_function(cls.validate)

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid objectid")
        return ObjectId(v)

    @classmethod
    def __get_pydantic_json_schema__(cls, field_schema):
        field_schema.update(type="string")
        return field_schema


class WorkScheduleDay(BaseModel):
    start: str = Field(..., description="Start time in HH:MM format")
    end: str = Field(..., description="End time in HH:MM format")
    isWorkingDay: bool = Field(default=True)


class WorkSchedule(BaseModel):
    monday: WorkScheduleDay
    tuesday: WorkScheduleDay
    wednesday: WorkScheduleDay
    thursday: WorkScheduleDay
    friday: WorkScheduleDay
    saturday: WorkScheduleDay
    sunday: WorkScheduleDay


class UserBase(BaseModel):
    email: EmailStr
    firstName: str = Field(..., min_length=1, max_length=50)
    lastName: str = Field(..., min_length=1, max_length=50)
    role: str = Field(..., pattern="^(admin|manager|employee)$")
    department: str = Field(..., min_length=1, max_length=100)
    position: str = Field(..., min_length=1, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    isActive: bool = Field(default=True)
    profilePicture: Optional[str] = None
    workSchedule: Optional[WorkSchedule] = None


class UserCreate(UserBase):
    password: str = Field(..., min_length=6)


class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    firstName: Optional[str] = Field(None, min_length=1, max_length=50)
    lastName: Optional[str] = Field(None, min_length=1, max_length=50)
    role: Optional[str] = Field(None, pattern="^(admin|manager|employee)$")
    department: Optional[str] = Field(None, min_length=1, max_length=100)
    position: Optional[str] = Field(None, min_length=1, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    isActive: Optional[bool] = None
    profilePicture: Optional[str] = None
    workSchedule: Optional[WorkSchedule] = None


class UserInDB(UserBase):
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    password: str
    hireDate: datetime
    createdAt: datetime
    updatedAt: datetime

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class User(UserBase):
    id: str = Field(alias="_id")
    hireDate: datetime
    createdAt: datetime
    updatedAt: datetime

    class Config:
        populate_by_name = True
        json_encoders = {ObjectId: str}


class UserProfile(BaseModel):
    id: str
    email: EmailStr
    firstName: str
    lastName: str
    role: str
    department: str
    position: str
    phone: Optional[str]
    profilePicture: Optional[str]
    hireDate: datetime
    workSchedule: Optional[WorkSchedule]
