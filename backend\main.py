from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from contextlib import asynccontextmanager
from datetime import datetime
import logging

from app.config import settings
from app.database import connect_to_mongo, close_mongo_connection
from app.routers import auth, users, attendance, leave_requests, manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await connect_to_mongo()

    # Run database migrations
    await run_migrations()

    yield
    # Shutdown
    await close_mongo_connection()


async def run_migrations():
    """Run database migrations on startup"""
    try:
        print("[STARTUP] Running database migrations...")

        # Import here to avoid circular imports
        from app.database import get_leave_requests_collection

        # Fix leave requests with missing updatedAt fields
        leave_requests_collection = get_leave_requests_collection()

        # Count records that need updating
        query = {
            "$or": [
                {"updatedAt": {"$exists": False}},
                {"updatedAt": None}
            ]
        }

        count = await leave_requests_collection.count_documents(query)

        if count > 0:
            print(f"[STARTUP] Fixing {count} leave request records with missing updatedAt...")

            # Update records - set updatedAt to createdAt for existing records
            update_result = await leave_requests_collection.update_many(
                query,
                [
                    {
                        "$set": {
                            "updatedAt": {
                                "$cond": {
                                    "if": {"$ifNull": ["$createdAt", False]},
                                    "then": "$createdAt",
                                    "else": datetime.utcnow()
                                }
                            }
                        }
                    }
                ]
            )

            print(f"[STARTUP] ✅ Fixed {update_result.modified_count} leave request records")
        else:
            print("[STARTUP] ✅ All leave request records are up to date")

    except Exception as e:
        print(f"[STARTUP] ⚠️ Error running migrations: {e}")
        # Don't fail startup for migration errors
        pass


# Create FastAPI app
app = FastAPI(
    title="Attendance Management API",
    description="FastAPI backend for Expo attendance tracking app",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions and ensure JSON response"""
    logger.error(f"HTTP Exception: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "error_code": f"HTTP_{exc.status_code}"
        }
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle validation errors and ensure JSON response"""
    logger.error(f"Validation Error: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": "Validation error",
            "errors": exc.errors()
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle all other exceptions and ensure JSON response"""
    logger.error(f"Unhandled Exception: {type(exc).__name__}: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "Internal server error",
            "error_code": "INTERNAL_ERROR"
        }
    )

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(users.router, prefix="/api/users", tags=["Users"])
app.include_router(attendance.router, prefix="/api/attendance", tags=["Attendance"])
app.include_router(leave_requests.router, prefix="/api/leave", tags=["Leave Requests"])
app.include_router(manager.router, prefix="/api/manager", tags=["Manager Dashboard"])


@app.get("/")
async def root():
    return {"message": "Attendance Management API", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    print("[HEALTH] Health check endpoint called")
    try:
        # Test database connection
        from app.database import get_users_collection
        users_collection = get_users_collection()
        user_count = await users_collection.count_documents({})
        print(f"[HEALTH] Database connection OK, {user_count} users found")

        return {
            "status": "healthy",
            "database": "connected",
            "users_count": user_count,
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        print(f"[HEALTH] Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


if __name__ == "__main__":
    import uvicorn
    print(f"🚀 Starting FastAPI server on {settings.api_host}:{settings.api_port}")
    print(f"📚 API Documentation: http://127.0.0.1:{settings.api_port}/docs")
    print(f"🔍 ReDoc: http://127.0.0.1:{settings.api_port}/redoc")
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )
