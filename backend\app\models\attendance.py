from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from bson import ObjectId
from .user import PyObjectId


class Location(BaseModel):
    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)
    address: Optional[str] = None


class AttendanceBase(BaseModel):
    date: datetime
    clockIn: Optional[datetime] = None
    clockOut: Optional[datetime] = None
    totalTime: int = Field(default=0, description="Total time in minutes")
    breakTime: int = Field(default=0, description="Break time in minutes")
    overtimeHours: int = Field(default=0, description="Overtime in minutes")
    status: str = Field(..., pattern="^(present|late|absent)$")
    location: Optional[Location] = None
    notes: Optional[str] = None
    isManualEntry: bool = Field(default=False)


class AttendanceCreate(BaseModel):
    location: Optional[Location] = None
    notes: Optional[str] = None


class AttendanceUpdate(BaseModel):
    clockOut: Optional[datetime] = None
    totalTime: Optional[int] = None
    breakTime: Optional[int] = None
    overtimeHours: Optional[int] = None
    status: Optional[str] = Field(None, pattern="^(present|late|absent)$")
    location: Optional[Location] = None
    notes: Optional[str] = None


class AttendanceInDB(AttendanceBase):
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    userId: PyObjectId
    createdAt: datetime
    updatedAt: datetime

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class AttendanceRecord(AttendanceBase):
    id: str = Field(alias="_id")
    userId: str
    createdAt: datetime
    updatedAt: datetime

    class Config:
        populate_by_name = True
        json_encoders = {ObjectId: str}


class AttendanceStatus(BaseModel):
    isClockedIn: bool
    clockInTime: Optional[datetime] = None
    clockOutTime: Optional[datetime] = None
    totalTime: int = Field(default=0)
    status: str = "absent"


class AttendanceStats(BaseModel):
    totalDays: int
    presentDays: int
    absentDays: int
    lateDays: int
    totalHours: int
    averageHours: float
    overtimeHours: int


class AttendanceHistory(BaseModel):
    records: list[AttendanceRecord]
    stats: AttendanceStats
    month: int
    year: int


class ClockActionPayload(BaseModel):
    location: Optional[Location] = None
    notes: Optional[str] = None
